/*
Projector control via Telnet for Optoma projectors
================================================================================
Project:       Custom Projector Control
Programmer:    Adapted from <PERSON>'s script, revised by Grok
Info:          Originally for Wesotronic, modified for Optoma projectors
Version:       v2.0
Date:          March 24, 2025
Client:        [Your Name/Project]
================================================================================
*/

var projector = {
    bootProjector: 1,
    powerButtons: ["d3001", "d3002", "d3101", "d3102"], // Proj1 On, Off; Proj2 On, Off
    muteButtons: ["d3003", "d3004", "d3103", "d3104"],   // Proj1 Mute On, Off; Proj2 Mute On, Off
    projectorSelect: ["d3011", "d3012"],                 // Proj1 Select, Proj2 Select
    activeProjector: 0,
    powerOn: "~0000 1\r",
    powerOff: "~0000 0\r",
    avMuteOn: "~0002 1\r",
    avMuteOff: "~0002 0\r",
    checkPowerState: "~00124 1\r",
    checkAvMuteState: "~00355 1\r",
    queryTimeout: 5000, // Timeout for queries in milliseconds
    countdownJoins: { /* as above */ },
    countdownTimers: {}, // To track active timers
    countdownJoins: {
        "d3001": "s3001", // Proj1 Power On
        "d3002": "s3002", // Proj1 Power Off
        "d3003": "s3003", // Proj1 AV Mute On
        "d3004": "s3004", // Proj1 AV Mute Off
        "d3101": "s3101", // Proj2 Power On
        "d3102": "s3102", // Proj2 Power Off
        "d3103": "s3103", // Proj2 AV Mute On
        "d3104": "s3104"  // Proj2 AV Mute Off
    },

    init: function () {
        CF.log("Projector Initializing...");
        projector.activeProjector = String.fromCharCode(parseInt(projector.bootProjector));
        CF.watch(CF.ObjectPressedEvent, projector.powerButtons, projector.powerPressed);
        CF.watch(CF.ObjectPressedEvent, projector.muteButtons, projector.mutePressed);
        CF.watch(CF.ObjectPressedEvent, projector.projectorSelect, projector.projectorSelect);

        projector.restoreStates();
        projector1.startQueryLoop();
        projector2.startQueryLoop();

        switch (projector.activeProjector) {
            case "\x01": CF.setJoin(projector.projectorSelect[0], 1); break;
            case "\x02": CF.setJoin(projector.projectorSelect[1], 1); break;
            default: CF.log("No projector active");
        }
    },

    startCountdown: function(textJoin, duration) {
        // Stop any existing countdown for this text join
        if (projector.countdownTimers[textJoin]) {
            clearInterval(projector.countdownTimers[textJoin]);
        }

        var count = duration;
        CF.setJoin(textJoin, count); // Display initial value

        var intervalId = setInterval(function() {
            count--;
            if (count >= 0) {
                CF.setJoin(textJoin, count); // Update countdown
            } else {
                clearInterval(intervalId);
                delete projector.countdownTimers[textJoin];
                CF.setJoin(textJoin, ""); // Clear text when done
            }
        }, 1000); // Update every second

        projector.countdownTimers[textJoin] = intervalId; // Store timer ID
    },

    restoreStates: function () {
        CF.getProperties("projectorStates", function (states) {
            var storedStates = states ? JSON.parse(states) : {
                projector1: { power: 0, mute: 0 },
                projector2: { power: 0, mute: 0 }
            };
            CF.setJoin("d3001", storedStates.projector1.power);
            CF.setJoin("d3002", storedStates.projector1.power === 1 ? 0 : 1);
            CF.setJoin("d3003", storedStates.projector1.mute);
            CF.setJoin("d3004", storedStates.projector1.mute === 1 ? 0 : 1);
            CF.setJoin("d3101", storedStates.projector2.power);
            CF.setJoin("d3102", storedStates.projector2.power === 1 ? 0 : 1);
            CF.setJoin("d3103", storedStates.projector2.mute);
            CF.setJoin("d3104", storedStates.projector2.mute === 1 ? 0 : 1);
            CF.log("Restored states: " + JSON.stringify(storedStates));
        });
    },

    saveStates: function () {
        var states = {
            projector1: { power: CF.getJoin("d3001") ? 1 : 0, mute: CF.getJoin("d3003") ? 1 : 0 },
            projector2: { power: CF.getJoin("d3101") ? 1 : 0, mute: CF.getJoin("d3103") ? 1 : 0 }
        };
        CF.setProperties("projectorStates", JSON.stringify(states));
        CF.log("Saved states: " + JSON.stringify(states));
    },

    projectorSelect: function (join, value, tokens, tags) {
        var tempProjector = join.slice(4, 5);
        CF.setJoin("PROJECTOR", 0);
        CF.setJoin(join, 1);
        projector.activeProjector = String.fromCharCode(parseInt(tempProjector));
        CF.log("Active Projector: " + projector.activeProjector.charCodeAt(0));
    },

    powerPressed: function(join, value, tokens, tags) {
        var command = "";
        switch (join) {
            case projector.powerButtons[0]: // d3001
                command = projector.powerOn;
                projector1.send(command, "power", false);
                break;
            case projector.powerButtons[1]: // d3002
                command = projector.powerOff;
                projector1.send(command, "power", false);
                break;
            case projector.powerButtons[2]: // d3101
                command = projector.powerOn;
                projector2.send(command, "power", false);
                break;
            case projector.powerButtons[3]: // d3102
                command = projector.powerOff;
                projector2.send(command, "power", false);
                break;
        }
        CF.log("Power Command Sent: " + command);
    
        // Start 10-second countdown
        var textJoin = projector.countdownJoins[join];
        if (textJoin) {
            projector.startCountdown(textJoin, 20);
        }
    },

    mutePressed: function(join, value, tokens, tags) {
        var command = "";
        switch (join) {
            case projector.muteButtons[0]: // d3003
                command = projector.avMuteOn;
                projector1.send(command, "mute", false);
                break;
            case projector.muteButtons[1]: // d3004
                command = projector.avMuteOff;
                projector1.send(command, "mute", false);
                break;
            case projector.muteButtons[2]: // d3103
                command = projector.avMuteOn;
                projector2.send(command, "mute", false);
                break;
            case projector.muteButtons[3]: // d3104
                command = projector.avMuteOff;
                projector2.send(command, "mute", false);
                break;
        }
        CF.log("Mute Command Sent: " + command);
    
        // Start 5-second countdown
        var textJoin = projector.countdownJoins[join];
        if (textJoin) {
            projector.startCountdown(textJoin, 4);
        }
    },
};
CF.modules.push({ name: "Optoma Projector Module", setup: projector.init });

var projector1 = {
    commandQueue: [],
    systemName: "Projector1",
    feedbackName: "raw_Projector1",
    connectionDJoin: "d113",
    connectionSJoin: "s113",
    queryInProgress: false,
    queryTimeoutId: null,


    init: function () {
        CF.log("Projector1 Initializing...");
        CF.watch(CF.FeedbackMatchedEvent, projector1.systemName, projector1.feedbackName, projector1.parseData);
        CF.watch(CF.ConnectionStatusChangeEvent, projector1.systemName, projector1.onConnectionChange, true);
    },

    startQueryLoop: function () {
        projector1.queryNextState();
    },

    send: function (data, commandType, isQuery) {
        if (isQuery) {
            if (projector1.queryInProgress) {
                CF.log("Projector1: Query in progress, queuing: " + data);
                projector1.commandQueue.push({ command: data, type: commandType });
                return;
            }
            projector1.queryInProgress = true;
            projector1.commandQueue.push({ command: data, type: commandType });
            projector1.queryTimeoutId = setTimeout(function () {
                CF.log("Projector1: Query timeout for: " + data);
                projector1.queryInProgress = false;
                projector1.commandQueue.shift();
                projector1.queryNextState();
            }, projector.queryTimeout);
        }
        CF.send(projector1.systemName, data);
    },

    queryNextState: function () {
        if (projector1.commandQueue.length > 0 || projector1.queryInProgress) return;
        projector1.send(projector.checkPowerState, "power", true);
    },

    parseData: function (fbName, rawdata) {
        CF.log("Projector1 Feedback: " + rawdata);
        var response = rawdata.toLowerCase().trim();
        if (projector1.commandQueue.length === 0) {
            CF.log("Projector1: No pending queries, ignoring: " + response);
            return;
        }

        var currentCommand = projector1.commandQueue[0];
        var commandType = currentCommand.type;

        if (response.includes("ok1") || response.includes("ok0")) {
            clearTimeout(projector1.queryTimeoutId);
            projector1.queryInProgress = false;
            projector1.commandQueue.shift();

            if (commandType === "power") {
                CF.setJoin("d3001", response.includes("ok1") ? 1 : 0);
                CF.setJoin("d3002", response.includes("ok1") ? 0 : 1);
                CF.log("Projector1 Power: " + (response.includes("ok1") ? "On" : "Off"));
                projector1.send(projector.checkAvMuteState, "mute", true); // Next query
            } else if (commandType === "mute") {
                CF.setJoin("d3003", response.includes("ok1") ? 1 : 0);
                CF.setJoin("d3004", response.includes("ok1") ? 0 : 1);
                CF.log("Projector1 AV Mute: " + (response.includes("ok1") ? "On" : "Off"));
                projector1.queryNextState(); // Continue loop
            }
            projector.saveStates();
        } else {
            CF.log("Projector1: Ignoring unsolicited response: " + response);
        }
    },

    onConnectionChange: function (system, connected, remote) {
        if (connected) {
            CF.log("Projector1 Connected: " + remote);
            CF.setJoin(projector1.connectionDJoin, 1);
            CF.setJoin(projector1.connectionSJoin, remote);
            projector1.startQueryLoop();
        } else {
            CF.log("Projector1 Disconnected");
            CF.setJoin(projector1.connectionDJoin, 0);
            CF.setJoin(projector1.connectionSJoin, "no connection");
            CF.setJoin("d3001", 0); CF.setJoin("d3002", 0);
            CF.setJoin("d3003", 0); CF.setJoin("d3004", 0);
            projector1.queryInProgress = false;
            projector1.commandQueue = [];
            clearTimeout(projector1.queryTimeoutId);
        }
    }
};
CF.modules.push({ name: "Optoma Projector 1 Module", setup: projector1.init });

var projector2 = {
    commandQueue: [],
    systemName: "Projector2",
    feedbackName: "raw_Projector2",
    connectionDJoin: "d114",
    connectionSJoin: "s114",
    queryInProgress: false,
    queryTimeoutId: null,

    init: function () {
        CF.log("Projector2 Initializing...");
        CF.watch(CF.FeedbackMatchedEvent, projector2.systemName, projector2.feedbackName, projector2.parseData);
        CF.watch(CF.ConnectionStatusChangeEvent, projector2.systemName, projector2.onConnectionChange, true);
    },

    startQueryLoop: function () {
        projector2.queryNextState();
    },

    send: function (data, commandType, isQuery) {
        if (isQuery) {
            if (projector2.queryInProgress) {
                CF.log("Projector2: Query in progress, queuing: " + data);
                projector2.commandQueue.push({ command: data, type: commandType });
                return;
            }
            projector2.queryInProgress = true;
            projector2.commandQueue.push({ command: data, type: commandType });
            projector2.queryTimeoutId = setTimeout(function () {
                CF.log("Projector2: Query timeout for: " + data);
                projector2.queryInProgress = false;
                projector2.commandQueue.shift();
                projector2.queryNextState();
            }, projector.queryTimeout);
        }
        CF.send(projector2.systemName, data);
    },

    queryNextState: function () {
        if (projector2.commandQueue.length > 0 || projector2.queryInProgress) return;
        projector2.send(projector.checkPowerState, "power", true);
    },

    parseData: function (fbName, rawdata) {
        CF.log("Projector2 Feedback: " + rawdata);
        var response = rawdata.toLowerCase().trim();
        if (projector2.commandQueue.length === 0) {
            CF.log("Projector2: No pending queries, ignoring: " + response);
            return;
        }

        var currentCommand = projector2.commandQueue[0];
        var commandType = currentCommand.type;

        if (response.includes("ok1") || response.includes("ok0")) {
            clearTimeout(projector2.queryTimeoutId);
            projector2.queryInProgress = false;
            projector2.commandQueue.shift();

            if (commandType === "power") {
                CF.setJoin("d3101", response.includes("ok1") ? 1 : 0);
                CF.setJoin("d3102", response.includes("ok1") ? 0 : 1);
                CF.log("Projector2 Power: " + (response.includes("ok1") ? "On" : "Off"));
                projector2.send(projector.checkAvMuteState, "mute", true); // Next query
            } else if (commandType === "mute") {
                CF.setJoin("d3103", response.includes("ok1") ? 1 : 0);
                CF.setJoin("d3104", response.includes("ok1") ? 0 : 1);
                CF.log("Projector2 AV Mute: " + (response.includes("ok1") ? "On" : "Off"));
                projector2.queryNextState(); // Continue loop
            }
            projector.saveStates();
        } else {
            CF.log("Projector2: Ignoring unsolicited response: " + response);
        }
    },

    onConnectionChange: function (system, connected, remote) {
        if (connected) {
            CF.log("Projector2 Connected: " + remote);
            CF.setJoin(projector2.connectionDJoin, 1);
            CF.setJoin(projector2.connectionSJoin, remote);
            projector2.startQueryLoop();
        } else {
            CF.log("Projector2 Disconnected");
            CF.setJoin(projector2.connectionDJoin, 0);
            CF.setJoin(projector2.connectionSJoin, "no connection");
            CF.setJoin("d3101", 0); CF.setJoin("d3102", 0);
            CF.setJoin("d3103", 0); CF.setJoin("d3104", 0);
            projector2.queryInProgress = false;
            projector2.commandQueue = [];
            clearTimeout(projector2.queryTimeoutId);
        }
    }
};
CF.modules.push({ name: "Optoma Projector 2 Module", setup: projector2.init });