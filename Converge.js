/*
Projector control via Telnet for Optoma projectors
================================================================================
Project:       Custom Projector Control
Programmer:    Adapted from <PERSON>'s script, revised by Grok
Info:          Originally for Wesotronic, modified for Optoma projectors
Version:       v2.0
Date:          March 24, 2025
Client:        [Your Name/Project]
================================================================================
*/

var projector = {
    bootProjector: 1,
    powerButtons: ["d3001", "d3002", "d3101", "d3102"], // Proj1 On, Off; Proj2 On, Off
    muteButtons: ["d3003", "d3004", "d3103", "d3104"],   // Proj1 Mute On, Off; Proj2 Mute On, Off
    projectorSelect: ["d3011", "d3012"],                 // Proj1 Select, Proj2 Select
    activeProjector: 0,
    powerOn: "~0000 1\r",
    powerOff: "~0000 0\r",
    avMuteOn: "~0002 1\r",
    avMuteOff: "~0002 0\r",
    checkPowerState: "~00124 1\r",
    checkAvMuteState: "~00355 1\r",
    queryTimeout: 3000, // Reduced timeout for faster response
    queryInterval: 10000, // Query every 10 seconds instead of continuously
    countdownTimers: {}, // To track active timers
    buttonDebounce: {}, // To prevent rapid button presses
    debounceDelay: 500, // 500ms debounce delay
    stateChanged: false, // Track if state needs saving
    saveStateTimer: null, // Debounced state saving
    countdownJoins: {
        "d3001": "s3001", // Proj1 Power On
        "d3002": "s3002", // Proj1 Power Off
        "d3003": "s3003", // Proj1 AV Mute On
        "d3004": "s3004", // Proj1 AV Mute Off
        "d3101": "s3101", // Proj2 Power On
        "d3102": "s3102", // Proj2 Power Off
        "d3103": "s3103", // Proj2 AV Mute On
        "d3104": "s3104"  // Proj2 AV Mute Off
    },

    init: function () {
        CF.log("Projector Initializing...");
        projector.activeProjector = String.fromCharCode(parseInt(projector.bootProjector));
        CF.watch(CF.ObjectPressedEvent, projector.powerButtons, projector.powerPressed);
        CF.watch(CF.ObjectPressedEvent, projector.muteButtons, projector.mutePressed);
        CF.watch(CF.ObjectPressedEvent, projector.projectorSelect, projector.projectorSelect);

        projector.restoreStates();
        projector1.startQueryLoop();
        projector2.startQueryLoop();

        switch (projector.activeProjector) {
            case "\x01": CF.setJoin(projector.projectorSelect[0], 1); break;
            case "\x02": CF.setJoin(projector.projectorSelect[1], 1); break;
            default: CF.log("No projector active");
        }
    },

    startCountdown: function(textJoin, duration) {
        // Stop any existing countdown for this text join
        if (projector.countdownTimers[textJoin]) {
            clearInterval(projector.countdownTimers[textJoin]);
        }

        var count = duration;
        CF.setJoin(textJoin, count); // Display initial value

        var intervalId = setInterval(function() {
            count--;
            if (count >= 0) {
                CF.setJoin(textJoin, count); // Update countdown
            } else {
                clearInterval(intervalId);
                delete projector.countdownTimers[textJoin];
                CF.setJoin(textJoin, ""); // Clear text when done
            }
        }, 1000); // Update every second

        projector.countdownTimers[textJoin] = intervalId; // Store timer ID
    },

    restoreStates: function () {
        CF.getProperties("projectorStates", function (states) {
            var storedStates = states ? JSON.parse(states) : {
                projector1: { power: 0, mute: 0 },
                projector2: { power: 0, mute: 0 }
            };
            CF.setJoin("d3001", storedStates.projector1.power);
            CF.setJoin("d3002", storedStates.projector1.power === 1 ? 0 : 1);
            CF.setJoin("d3003", storedStates.projector1.mute);
            CF.setJoin("d3004", storedStates.projector1.mute === 1 ? 0 : 1);
            CF.setJoin("d3101", storedStates.projector2.power);
            CF.setJoin("d3102", storedStates.projector2.power === 1 ? 0 : 1);
            CF.setJoin("d3103", storedStates.projector2.mute);
            CF.setJoin("d3104", storedStates.projector2.mute === 1 ? 0 : 1);
            CF.log("Restored states: " + JSON.stringify(storedStates));
        });
    },

    saveStates: function () {
        // Debounced state saving to reduce I/O operations
        if (projector.saveStateTimer) {
            clearTimeout(projector.saveStateTimer);
        }
        projector.saveStateTimer = setTimeout(function() {
            var states = {
                projector1: { power: CF.getJoin("d3001") ? 1 : 0, mute: CF.getJoin("d3003") ? 1 : 0 },
                projector2: { power: CF.getJoin("d3101") ? 1 : 0, mute: CF.getJoin("d3103") ? 1 : 0 }
            };
            CF.setProperties("projectorStates", JSON.stringify(states));
            projector.stateChanged = false;
        }, 1000); // Save after 1 second of no changes
    },

    projectorSelect: function (join) {
        var tempProjector = join.slice(4, 5);
        CF.setJoin("PROJECTOR", 0);
        CF.setJoin(join, 1);
        projector.activeProjector = String.fromCharCode(parseInt(tempProjector));
    },

    powerPressed: function(join) {
        // Debounce button presses
        if (projector.buttonDebounce[join]) {
            return; // Ignore rapid button presses
        }
        projector.buttonDebounce[join] = true;
        setTimeout(function() {
            delete projector.buttonDebounce[join];
        }, projector.debounceDelay);

        var command = "";
        switch (join) {
            case projector.powerButtons[0]: // d3001
                command = projector.powerOn;
                projector1.send(command, "power", false);
                break;
            case projector.powerButtons[1]: // d3002
                command = projector.powerOff;
                projector1.send(command, "power", false);
                break;
            case projector.powerButtons[2]: // d3101
                command = projector.powerOn;
                projector2.send(command, "power", false);
                break;
            case projector.powerButtons[3]: // d3102
                command = projector.powerOff;
                projector2.send(command, "power", false);
                break;
        }

        // Start countdown
        var textJoin = projector.countdownJoins[join];
        if (textJoin) {
            projector.startCountdown(textJoin, 20);
        }
    },

    mutePressed: function(join) {
        // Debounce button presses
        if (projector.buttonDebounce[join]) {
            return; // Ignore rapid button presses
        }
        projector.buttonDebounce[join] = true;
        setTimeout(function() {
            delete projector.buttonDebounce[join];
        }, projector.debounceDelay);

        var command = "";
        switch (join) {
            case projector.muteButtons[0]: // d3003
                command = projector.avMuteOn;
                projector1.send(command, "mute", false);
                break;
            case projector.muteButtons[1]: // d3004
                command = projector.avMuteOff;
                projector1.send(command, "mute", false);
                break;
            case projector.muteButtons[2]: // d3103
                command = projector.avMuteOn;
                projector2.send(command, "mute", false);
                break;
            case projector.muteButtons[3]: // d3104
                command = projector.avMuteOff;
                projector2.send(command, "mute", false);
                break;
        }

        // Start countdown
        var textJoin = projector.countdownJoins[join];
        if (textJoin) {
            projector.startCountdown(textJoin, 4);
        }
    },
};
CF.modules.push({ name: "Optoma Projector Module", setup: projector.init });

var projector1 = {
    commandQueue: [],
    systemName: "Projector1",
    feedbackName: "raw_Projector1",
    connectionDJoin: "d113",
    connectionSJoin: "s113",
    queryInProgress: false,
    queryTimeoutId: null,
    queryIntervalId: null,
    connected: false,

    init: function () {
        CF.watch(CF.FeedbackMatchedEvent, projector1.systemName, projector1.feedbackName, projector1.parseData);
        CF.watch(CF.ConnectionStatusChangeEvent, projector1.systemName, projector1.onConnectionChange, true);
    },

    startQueryLoop: function () {
        if (projector1.queryIntervalId) {
            clearInterval(projector1.queryIntervalId);
        }
        // Query immediately, then set up interval
        projector1.queryNextState();
        projector1.queryIntervalId = setInterval(function() {
            if (projector1.connected && !projector1.queryInProgress) {
                projector1.queryNextState();
            }
        }, projector.queryInterval);
    },

    send: function (data, commandType, isQuery) {
        if (isQuery) {
            if (projector1.queryInProgress) {
                projector1.commandQueue.push({ command: data, type: commandType });
                return;
            }
            projector1.queryInProgress = true;
            projector1.commandQueue.push({ command: data, type: commandType });
            projector1.queryTimeoutId = setTimeout(function () {
                projector1.queryInProgress = false;
                projector1.commandQueue.shift();
                projector1.processQueue();
            }, projector.queryTimeout);
        }
        CF.send(projector1.systemName, data);
    },

    processQueue: function () {
        if (projector1.commandQueue.length > 0 && !projector1.queryInProgress) {
            var nextCommand = projector1.commandQueue[0];
            projector1.send(nextCommand.command, nextCommand.type, true);
        }
    },

    queryNextState: function () {
        if (projector1.commandQueue.length > 0 || projector1.queryInProgress || !projector1.connected) return;
        projector1.send(projector.checkPowerState, "power", true);
    },

    parseData: function (fbName, rawdata) {
        var response = rawdata.toLowerCase().trim();
        if (projector1.commandQueue.length === 0) {
            return; // Ignore unsolicited responses
        }

        var currentCommand = projector1.commandQueue[0];
        var commandType = currentCommand.type;

        if (response.includes("ok1") || response.includes("ok0")) {
            clearTimeout(projector1.queryTimeoutId);
            projector1.queryInProgress = false;
            projector1.commandQueue.shift();

            var isOn = response.includes("ok1");
            if (commandType === "power") {
                CF.setJoin("d3001", isOn ? 1 : 0);
                CF.setJoin("d3002", isOn ? 0 : 1);
                projector1.send(projector.checkAvMuteState, "mute", true); // Next query
            } else if (commandType === "mute") {
                CF.setJoin("d3003", isOn ? 1 : 0);
                CF.setJoin("d3004", isOn ? 0 : 1);
                projector1.processQueue(); // Process any queued commands
            }

            // Mark state as changed for debounced saving
            projector.stateChanged = true;
            projector.saveStates();
        }
    },

    onConnectionChange: function (system, connected, remote) {
        projector1.connected = connected;
        if (connected) {
            CF.setJoin(projector1.connectionDJoin, 1);
            CF.setJoin(projector1.connectionSJoin, remote);
            projector1.startQueryLoop();
        } else {
            CF.setJoin(projector1.connectionDJoin, 0);
            CF.setJoin(projector1.connectionSJoin, "no connection");
            CF.setJoin("d3001", 0); CF.setJoin("d3002", 0);
            CF.setJoin("d3003", 0); CF.setJoin("d3004", 0);
            projector1.queryInProgress = false;
            projector1.commandQueue = [];
            clearTimeout(projector1.queryTimeoutId);
            if (projector1.queryIntervalId) {
                clearInterval(projector1.queryIntervalId);
                projector1.queryIntervalId = null;
            }
        }
    }
};
CF.modules.push({ name: "Optoma Projector 1 Module", setup: projector1.init });

var projector2 = {
    commandQueue: [],
    systemName: "Projector2",
    feedbackName: "raw_Projector2",
    connectionDJoin: "d114",
    connectionSJoin: "s114",
    queryInProgress: false,
    queryTimeoutId: null,
    queryIntervalId: null,
    connected: false,

    init: function () {
        CF.watch(CF.FeedbackMatchedEvent, projector2.systemName, projector2.feedbackName, projector2.parseData);
        CF.watch(CF.ConnectionStatusChangeEvent, projector2.systemName, projector2.onConnectionChange, true);
    },

    startQueryLoop: function () {
        if (projector2.queryIntervalId) {
            clearInterval(projector2.queryIntervalId);
        }
        // Query immediately, then set up interval
        projector2.queryNextState();
        projector2.queryIntervalId = setInterval(function() {
            if (projector2.connected && !projector2.queryInProgress) {
                projector2.queryNextState();
            }
        }, projector.queryInterval);
    },

    send: function (data, commandType, isQuery) {
        if (isQuery) {
            if (projector2.queryInProgress) {
                projector2.commandQueue.push({ command: data, type: commandType });
                return;
            }
            projector2.queryInProgress = true;
            projector2.commandQueue.push({ command: data, type: commandType });
            projector2.queryTimeoutId = setTimeout(function () {
                projector2.queryInProgress = false;
                projector2.commandQueue.shift();
                projector2.processQueue();
            }, projector.queryTimeout);
        }
        CF.send(projector2.systemName, data);
    },

    processQueue: function () {
        if (projector2.commandQueue.length > 0 && !projector2.queryInProgress) {
            var nextCommand = projector2.commandQueue[0];
            projector2.send(nextCommand.command, nextCommand.type, true);
        }
    },

    queryNextState: function () {
        if (projector2.commandQueue.length > 0 || projector2.queryInProgress || !projector2.connected) return;
        projector2.send(projector.checkPowerState, "power", true);
    },

    parseData: function (fbName, rawdata) {
        var response = rawdata.toLowerCase().trim();
        if (projector2.commandQueue.length === 0) {
            return; // Ignore unsolicited responses
        }

        var currentCommand = projector2.commandQueue[0];
        var commandType = currentCommand.type;

        if (response.includes("ok1") || response.includes("ok0")) {
            clearTimeout(projector2.queryTimeoutId);
            projector2.queryInProgress = false;
            projector2.commandQueue.shift();

            var isOn = response.includes("ok1");
            if (commandType === "power") {
                CF.setJoin("d3101", isOn ? 1 : 0);
                CF.setJoin("d3102", isOn ? 0 : 1);
                projector2.send(projector.checkAvMuteState, "mute", true); // Next query
            } else if (commandType === "mute") {
                CF.setJoin("d3103", isOn ? 1 : 0);
                CF.setJoin("d3104", isOn ? 0 : 1);
                projector2.processQueue(); // Process any queued commands
            }

            // Mark state as changed for debounced saving
            projector.stateChanged = true;
            projector.saveStates();
        }
    },

    onConnectionChange: function (system, connected, remote) {
        projector2.connected = connected;
        if (connected) {
            CF.setJoin(projector2.connectionDJoin, 1);
            CF.setJoin(projector2.connectionSJoin, remote);
            projector2.startQueryLoop();
        } else {
            CF.setJoin(projector2.connectionDJoin, 0);
            CF.setJoin(projector2.connectionSJoin, "no connection");
            CF.setJoin("d3101", 0); CF.setJoin("d3102", 0);
            CF.setJoin("d3103", 0); CF.setJoin("d3104", 0);
            projector2.queryInProgress = false;
            projector2.commandQueue = [];
            clearTimeout(projector2.queryTimeoutId);
            if (projector2.queryIntervalId) {
                clearInterval(projector2.queryIntervalId);
                projector2.queryIntervalId = null;
            }
        }
    }
};
CF.modules.push({ name: "Optoma Projector 2 Module", setup: projector2.init });