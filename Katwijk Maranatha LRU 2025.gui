﻿<?xml version="1.0" encoding="utf-8"?>
<gui>
  <properties>
    <project><PERSON><PERSON><PERSON></project>
    <designer>LRU</designer>
    <size autoposition="scale">
      <portrait width="1055" height="1700" />
      <landscape width="1700" height="1055" />
    </size>
    <autoposition />
    <imagefolder>
    </imagefolder>
    <debug loaderrors="0" connectionerrors="0" />
    <devices>
      <device name="ZDMID APPC-10SLB testscherm PKO">64066DBBEA419A8D7D772EF5B7BF8F5B</device>
      <device name="Touchpanel Hans">64B9680162C4AA550CA3D073C7F89E08</device>
      <device name="Woerden - G<PERSON> Kerkzaal">AD0057C09D965591653B5754A726E2F4</device>
      <device name="Woerden - GG - Zalen">3F41A713B2EA0093A844A80EC9834E2F</device>
      <device name="GG Leerdam Kerkzaal">28C2910CC754E2FA0E9FA9F53FB831FE</device>
      <device name="GG Leerdam Zaal">B6FC20AB4819D6FBA13C1694763893</device>
      <device name="GG Leerdam Zalen">B6FC20AB4819D6F9FBA13C1694763893</device>
      <device name="Development Device">617B4260D1F3922AFA06698AEC306D93</device>
      <device name="Retina iPad Bram">0E78925A07E7CB7C71CC63B958C093F4</device>
      <device name="Samsung S20 Bram">481DCD9094B052BAD50EF6F30E08271C</device>
      <device name="GG Nunspeet Kosterbank">9CD99BB7C75FE064D7C56C3EE90615E6</device>
      <device name="GG Nunspeet Kantoor koster">8E6A35EEB7F31950725FE2B38E126E6D</device>
      <device name="GG Nunspeet tablet koster">39E991AFEDA59D7F3546B8C50D2CB89B</device>
      <device name="Waarde patchkast">1E7804196C02927ADE67E90D144D1697</device>
      <device name="Waarde zalen">1171E2535E51912CD431330F1A5FC7F1</device>
      <device name="Waarde koster">DCE680DDAC0A89C0186CEE695E797799</device>
      <device name="Kerkenraad Gorinchem">980946D9C5F9FFD823036731122D99F8</device>
      <device name="GG Gorinchem koster">0DDE813302FBE67473CB827D3EB814FE</device>
      <device name="Katwijk maranatha">44B0422879CE9A94B021A2F336542B45</device>
    </devices>
  </properties>
  <systems>
    <system name="MUXLAB-TCPLINK" protocol="tcp" accept="0" ip="***************" port="23" origin="0" alwayson="1" idleTimeout="0" dialogTimeout="0" heartbeatMode="-1" heartbeatRx="" heartbeatTx="" textEncoding="" eom="" js="" connectionStatus="0" disconnectionStatus="0" startupCmd="" startupMacro="" offlinequeue="1" ssl="0">
      <fb name="raw_MUXLAB-TCPLINK" regex="." />
    </system>
    <system name="Nexia" protocol="tcp" accept="0" ip="***************" port="23" origin="0" alwayson="1" idleTimeout="0" dialogTimeout="0" heartbeatMode="-1" heartbeatRx="" heartbeatTx="" textEncoding="" eom="\x0D\x0A" js="" connectionStatus="21" disconnectionStatus="22" startupCmd="" startupMacro="" offlinequeue="1" ssl="0">
      <fb name="raw_Nexia" regex="." />
    </system>
    <system name="Camera1" protocol="tcp" accept="0" ip="***************" port="5678" origin="0" alwayson="1" idleTimeout="0" dialogTimeout="0" heartbeatMode="-1" heartbeatRx="" heartbeatTx="" textEncoding="" eom="" js="" connectionStatus="0" disconnectionStatus="0" startupCmd="" startupMacro="" offlinequeue="1" ssl="0">
      <fb name="raw_Camera1" regex="." />
    </system>
    <system name="Projector1" protocol="tcp" accept="0" ip="***************" port="23" origin="0" alwayson="1" idleTimeout="0" dialogTimeout="0" heartbeatMode="-1" heartbeatRx="" heartbeatTx="" textEncoding="" eom="" js="" connectionStatus="0" disconnectionStatus="0" startupCmd="" startupMacro="" offlinequeue="1" ssl="0">
      <fb name="raw_Projector1" regex="." />
    </system>
    <system name="Projector2" protocol="tcp" accept="0" ip="***************" port="23" origin="0" alwayson="1" idleTimeout="0" dialogTimeout="0" heartbeatMode="-1" heartbeatRx="" heartbeatTx="" textEncoding="" eom="" js="" connectionStatus="0" disconnectionStatus="0" startupCmd="" startupMacro="" offlinequeue="1" ssl="0">
      <fb name="raw_Projector2" regex="." />
    </system>
    <system name="SharpTV" protocol="tcp" accept="0" ip="***************" port="10002" origin="0" alwayson="1" idleTimeout="0" dialogTimeout="0" heartbeatMode="-1" heartbeatRx="" heartbeatTx="" textEncoding="" eom="" js="" connectionStatus="0" disconnectionStatus="0" startupCmd="" startupMacro="" offlinequeue="1" ssl="0">
      <fb name="raw_SharpTV" regex="." />
    </system>
  </systems>
  <themes>
    <theme type="gauge" name=".Slider[state='0']"><![CDATA[]]></theme>
    <theme type="gauge" name=".Slider[state='1']"><![CDATA[]]></theme>
    <theme type="text" name=".Text"><![CDATA[ color: #D4D4D4; font-size: 22px; font-family: 'Arial'; text-align: right; vertical-align: middle; font-weight: normal; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="background" name=".portrait_1"><![CDATA[ background-image: url(<EMAIL>); background-repeat: no-repeat;]]></theme>
    <theme type="input" name=".InputField"><![CDATA[ background-color: #FFFFFF; border-width: 0px; border-color: #292929; color: #414141; font-size: 20px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; text-align: left; vertical-align: middle; font-weight: bold; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="button" name=".Indicator[state='0']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(LED-Green.png); color: #FFFFFF; font-size: 12px; font-family: 'Verdana'; font-weight: normal; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Indicator[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(LED-Red.png); color: #FFFFFF; font-size: 12px; font-family: 'Verdana'; font-weight: normal; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Button_aan_uit[state='0']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Matrix-selector-On.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 22px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Button_aan_uit[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(generic button OFF.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 22px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Button_aan_uit_INV[state='0']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Matrix-selecto Offr.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 27px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Button_aan_uit_INV[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Matrix-selector-On.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 27px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="gauge" name=".Slider_1[state='0']"><![CDATA[]]></theme>
    <theme type="gauge" name=".Slider_1[state='1']"><![CDATA[]]></theme>
    <theme type="button" name=".Buttonzonderplaatje[state='0']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Wesotronic-Logo-audiovisueel_CMYK.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 14px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Buttonzonderplaatje[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Wesotronic-Logo-audiovisueel_CMYK.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 14px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="background" name=".portrait_2"><![CDATA[ background-image: url(<EMAIL>); background-repeat: no-repeat;]]></theme>
    <theme type="gauge" name=".Slider_2[state='0']"><![CDATA[]]></theme>
    <theme type="gauge" name=".Slider_2[state='1']"><![CDATA[]]></theme>
    <theme type="text" name=".SliderText"><![CDATA[ color: #FFFFFF; font-size: 20px; font-family: 'Arial'; text-align: right; vertical-align: top; font-weight: normal; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="button" name=".box[state='0']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(tick.png); color: #FFFFFF; font-size: 12px; font-family: 'Verdana'; font-weight: normal; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".box[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(box.png); color: #FFFFFF; font-size: 12px; font-family: 'Verdana'; font-weight: normal; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="text" name=".TickBoxText"><![CDATA[ color: #FFFFFF; font-size: 22px; font-family: 'Arial'; text-align: left; vertical-align: middle; font-weight: normal; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="text" name=".Title"><![CDATA[ color: #FFFFFF; font-size: 26px; font-family: 'Arial'; text-align: center; vertical-align: middle; font-weight: normal; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="text" name=".subTitle"><![CDATA[ color: #D8D8D8; font-size: 36px; font-name: 'Helvetica-BoldOblique'; font-family: 'Helvetica'; text-align: left; vertical-align: middle; font-weight: bold; font-style: italics; text-decoration: none; display: table-cell;]]></theme>
    <theme type="text" name=".logList"><![CDATA[ color: #FFFFFF; font-size: 18px; font-family: 'Arial'; text-align: left; vertical-align: middle; font-weight: normal; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="button" name=".box_INV[state='0']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(box.png); color: #FFFFFF; font-size: 12px; font-family: 'Verdana'; font-weight: normal; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".box_INV[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(tick.png); color: #FFFFFF; font-size: 12px; font-family: 'Verdana'; font-weight: normal; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="background" name=".portrait_3"><![CDATA[ background-image: url(<EMAIL>); background-repeat: no-repeat;]]></theme>
    <theme type="button" name=".Button_aan_uit_INV_1[state='0']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Matrix-selecto Offr.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 27px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Button_aan_uit_INV_1[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Matrix-selector-On1.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 27px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="background" name=".portrait_4"><![CDATA[ background-image: url(<EMAIL>); background-repeat: no-repeat;]]></theme>
    <theme type="background" name=".achtergrond_kerk"><![CDATA[ background-image: url(Waarde32.jpg); background-repeat: no-repeat;]]></theme>
    <theme type="text" name=".Status"><![CDATA[ color: #D4D4D4; font-size: 14px; font-name: 'Arial-BoldMT'; font-family: 'Arial'; text-align: left; vertical-align: top; font-weight: bold; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="background" name=".newtheme_7"><![CDATA[ background-image: url(56d84a07-9ea6-492b-bcd9-88c3cb66210c.jfif); background-repeat: no-repeat;]]></theme>
    <theme type="button" name=".Button_aan_uit_INV_2[state='0']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Matrix-selecto Offr.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 27px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Button_aan_uit_INV_2[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Matrix-selector-On1.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 27px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
  </themes>
  <scripts>
    <script name="main.js" />
    <script name="navigatie.js" />
    <script name="camera.js" />
    <script name="Muxlab.js" />
    <script name="nexia.js" />
    <script name="camera.js" />
    <script name="Converge.js" />
    <script name="Sharp.js" />
  </scripts>
  <page name="settings" folder="" j="0" tags="" transition="None" subtype="None" time="0" ease="">
    <portrait />
    <landscape t="portrait_1">
      <img x="806" y="10" w="894" h="253" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <btn j="1" x="946" y="35" w="754" h="203" t="Buttonzonderplaatje" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="Kerkzaal" sim="0" l="0" s="1" tags="">
        <inactive s="0">
        </inactive>
        <active s="0">
        </active>
      </btn>
      <txt j="116" x="699" y="20" w="270" h="26" t="Text" wrap="False" l="0" tags="" overrideFontAlignmentH="left" overrideFontAlignmentV="center">New Text Object</txt>
      <txt j="0" x="532" y="20" w="150" h="28" t="Text" wrap="False" l="0" tags="">Audio DSP IP</txt>
      <txt j="10002" x="404" y="48" w="469" h="26" t="Text" wrap="False" l="0" tags="" overrideFontAlignmentH="left" overrideFontAlignmentV="center">New Text Object</txt>
    </landscape>
  </page>
  <page name="Kerkzaal" folder="" j="0" tags="" transition="None" subtype="None" time="0" ease="">
    <portrait />
    <landscape t="portrait_1">
      <img x="5" y="8" w="1419" h="1041" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <btn j="505" x="292" y="885" w="230" h="170" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">Alle
mics
uit</inactive>
        <active s="0">Alle
mics
uit</active>
      </btn>
      <btn j="504" x="292" y="539" w="230" h="170" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">Kerk
met
Declamatie</inactive>
        <active s="0">Kerk
met
Declamatie</active>
      </btn>
      <btn j="506" x="292" y="712" w="230" h="170" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">Kerk
met
Presentatie</inactive>
        <active s="0">Kerk
met
Presentatie</active>
      </btn>
      <img x="50" y="20" w="146" h="1018" j="0" clickthrough="1" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">Empty-Slide-Track.png</img>
      <txt j="103701" x="162" y="1007" w="50" h="20" t="SliderText" wrap="False" l="0" tags="">-12</txt>
      <slider j="103701" d="0" x="10" y="60" w="204" h="959" min="-18" max="0" decimals="0" unit="decimal" t="Slider" sim="1" l="0" tags="">
        <indicator state="0" offsetX="-25" offsetY="10" x="0" y="0" w="0" h="0" imgW="100" imgH="170" t="">IN-OUT-Slider-Knob.png</indicator>
        <indicator state="1" offsetX="-25" offsetY="10" x="0" y="0" w="0" h="0" imgW="0" imgH="0" t="">IN-OUT-Slider-Knob.png</indicator>
      </slider>
      <video x="1228" y="389" w="464" h="0" j="0" play="0" stop="0" url="" bgcolor="" format="" auth_user="" auth_password="" auth_realm="" auth_host="" auth_method="" auth_proxy="" tags="" />
      <subpage name="navigatie" x="1430" y="5" j="0" transition1="" subtype1="" time1="0" ease1="" transition2="" subtype2="" time2="0" ease2="" clickthrough="0" topmost="0" v="1" l="0" tags="" />
      <btn j="503" x="292" y="366" w="230" h="170" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">Kerk
met
Zwanenhals</inactive>
        <active s="0">Kerk
met
Zwanenhals</active>
      </btn>
      <btn j="501" x="292" y="20" w="230" h="170" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">Zaal
zonder
kerk</inactive>
        <active s="0">Zaal
zonder
kerk</active>
      </btn>
      <btn j="502" x="292" y="193" w="230" h="170" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">Kerk
met
Rever</inactive>
        <active s="0">Kerk
met
Rever</active>
      </btn>
    </landscape>
  </page>
  <page name="StartUp" folder="" j="0" tags="" transition="None" subtype="None" time="0" ease="" start="1">
    <portrait />
    <landscape t="portrait_1">
      <subpage name="navigatie" x="1080" y="0" j="0" transition1="" subtype1="" time1="0" ease1="" transition2="" subtype2="" time2="0" ease2="" clickthrough="0" topmost="0" v="0" l="0" tags="" />
      <txt j="0" x="497" y="433" w="753" h="100" t="subTitle" wrap="False" l="0" tags="">Aan het verbinden...</txt>
    </landscape>
  </page>
  <page name="Camera" folder="" j="0" tags="" transition="None" subtype="None" time="0" ease="">
    <portrait />
    <landscape t="portrait_1">
      <img x="813" y="275" w="622" h="779" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <img x="3" y="275" w="799" h="780" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <txt j="0" x="102" y="291" w="601" h="61" t="subTitle" wrap="False" l="0" tags="" overrideFontColor="#FF8000" overrideFontAlignmentH="center" overrideFontAlignmentV="center"> Camera Presets</txt>
      <btn j="2111" x="0" y="408" w="272" h="184" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PRESET1">
        <inactive s="0">1</inactive>
        <active s="0">1</active>
      </btn>
      <btn j="2114" x="0" y="650" w="272" h="184" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PRESET1">
        <inactive s="0">4</inactive>
        <active s="0">4</active>
      </btn>
      <btn j="2117" x="0" y="869" w="272" h="184" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PRESET1">
        <inactive s="0">7</inactive>
        <active s="0">7</active>
      </btn>
      <btn j="2112" x="266" y="408" w="272" h="184" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PRESET1">
        <inactive s="0">2</inactive>
        <active s="0">2</active>
      </btn>
      <btn j="2115" x="266" y="650" w="272" h="184" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PRESET1">
        <inactive s="0">5</inactive>
        <active s="0">5</active>
      </btn>
      <btn j="2118" x="266" y="869" w="272" h="184" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PRESET1">
        <inactive s="0">8</inactive>
        <active s="0">8</active>
      </btn>
      <btn j="2001" x="985" y="363" w="291" h="182" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Op</inactive>
        <active s="0">Op</active>
      </btn>
      <btn j="2003" x="1137" y="526" w="291" h="182" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Rechts</inactive>
        <active s="0">Rechts</active>
      </btn>
      <btn j="2002" x="808" y="522" w="291" h="182" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Links</inactive>
        <active s="0">Links</active>
      </btn>
      <btn j="2004" x="985" y="683" w="291" h="182" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Neer</inactive>
        <active s="0">Neer</active>
      </btn>
      <btn j="2102" x="1142" y="865" w="280" h="182" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Zoom uit</inactive>
        <active s="0">Zoom uit</active>
      </btn>
      <btn j="2101" x="813" y="869" w="281" h="182" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Zoom in</inactive>
        <active s="0">Zoom in</active>
      </btn>
      <txt j="0" x="891" y="281" w="440" h="61" t="subTitle" wrap="False" l="0" tags="" overrideFontColor="#FF8000" overrideFontAlignmentH="center" overrideFontAlignmentV="center">Handbediening camera</txt>
      <btn j="2113" x="531" y="408" w="272" h="184" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PRESET1">
        <inactive s="0">3</inactive>
        <active s="0">3</active>
      </btn>
      <btn j="2116" x="531" y="650" w="272" h="184" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PRESET1">
        <inactive s="0">6</inactive>
        <active s="0">6</active>
      </btn>
      <btn j="2119" x="531" y="869" w="272" h="184" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PRESET1">
        <inactive s="0">9</inactive>
        <active s="0">9</active>
      </btn>
      <subpage name="navigatie" x="1430" y="5" j="0" transition1="" subtype1="" time1="0" ease1="" transition2="" subtype2="" time2="0" ease2="" clickthrough="0" topmost="0" v="1" l="0" tags="" />
    </landscape>
  </page>
  <page name="Matrix" folder="" j="0" tags="" transition="" subtype="" time="0" ease="">
    <portrait />
    <landscape t="portrait_1">
      <img x="1040" y="715" w="205" h="332" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <img x="1260" y="715" w="205" h="332" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <img x="7" y="5" w="205" h="332" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <img x="7" y="589" w="205" h="460" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <subpage name="navigatie" x="1430" y="0" j="0" transition1="" subtype1="" time1="0" ease1="" transition2="" subtype2="" time2="0" ease2="" clickthrough="0" topmost="0" v="1" l="0" tags="" />
      <txt j="0" x="33" y="0" w="141" h="87" t="subTitle" wrap="False" l="0" tags="" overrideFontAlignmentH="center" overrideFontAlignmentV="center">Beamer
Links</txt>
      <btn j="11" x="4" y="600" w="210" h="150" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">OBS</inactive>
        <active s="0">OBS</active>
      </btn>
      <btn j="12" x="4" y="751" w="210" h="150" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Presentatie</inactive>
        <active s="0">Presentatie</active>
      </btn>
      <btn j="13" x="4" y="905" w="210" h="150" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">HDMI
Podium</inactive>
        <active s="0">HDMI
Podium</active>
      </btn>
      <btn j="3001" x="4" y="84" w="210" h="124" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">Aan</inactive>
        <active s="0">Aan</active>
      </btn>
      <btn j="3002" x="4" y="204" w="210" h="124" t="Button_aan_uit_INV_1" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">Uit</inactive>
        <active s="0">Uit</active>
      </btn>
      <img x="227" y="588" w="205" h="461" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <btn j="21" x="224" y="600" w="210" h="150" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">OBS</inactive>
        <active s="0">OBS</active>
      </btn>
      <btn j="22" x="224" y="751" w="210" h="150" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Presentatie</inactive>
        <active s="0">Presentatie</active>
      </btn>
      <btn j="23" x="224" y="905" w="210" h="150" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">HDMI
Podium</inactive>
        <active s="0">HDMI
Podium</active>
      </btn>
      <img x="447" y="588" w="205" h="460" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <btn j="31" x="444" y="600" w="210" h="150" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">OBS</inactive>
        <active s="0">OBS</active>
      </btn>
      <btn j="32" x="444" y="751" w="210" h="150" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Presentatie</inactive>
        <active s="0">Presentatie</active>
      </btn>
      <btn j="33" x="444" y="905" w="210" h="150" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">HDMI
Podium</inactive>
        <active s="0">HDMI
Podium</active>
      </btn>
      <img x="227" y="5" w="205" h="332" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <txt j="0" x="253" y="0" w="141" h="87" t="subTitle" wrap="False" l="0" tags="" overrideFontAlignmentH="center" overrideFontAlignmentV="center">Beamer
Rechts</txt>
      <btn j="3101" x="224" y="84" w="210" h="124" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">Aan</inactive>
        <active s="0">Aan</active>
      </btn>
      <btn j="3102" x="224" y="204" w="210" h="124" t="Button_aan_uit_INV_1" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">Uit</inactive>
        <active s="0">Uit</active>
      </btn>
      <btn j="3003" x="1034" y="803" w="210" h="124" t="Button_aan_uit_INV_1" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">AV
Mute</inactive>
        <active s="0">AV
Mute</active>
      </btn>
      <btn j="3103" x="1254" y="803" w="210" h="124" t="Button_aan_uit_INV_1" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">AV
Mute</inactive>
        <active s="0">AV
Mute</active>
      </btn>
      <btn j="3004" x="1034" y="923" w="210" h="124" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">AV
Unmute</inactive>
        <active s="0">AV
Unmute</active>
      </btn>
      <btn j="3104" x="1254" y="923" w="210" h="124" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">AV
Unmute</inactive>
        <active s="0">AV
Unmute</active>
      </btn>
      <img x="447" y="5" w="205" h="333" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <txt j="0" x="473" y="0" w="141" h="87" t="subTitle" wrap="False" l="0" tags="" overrideFontAlignmentH="center" overrideFontAlignmentV="center">Scherm
Bijzaal</txt>
      <txt j="3001" x="39" y="162" w="144" h="23" t="Status" wrap="False" l="0" tags="">
      </txt>
      <txt j="3002" x="39" y="280" w="144" h="23" t="Status" wrap="False" l="0" tags="">
      </txt>
      <txt j="3003" x="1069" y="881" w="144" h="23" t="Status" wrap="False" l="0" tags="">
      </txt>
      <txt j="3004" x="1069" y="1002" w="144" h="23" t="Status" wrap="False" l="0" tags="">
      </txt>
      <txt j="3101" x="256" y="162" w="144" h="23" t="Status" wrap="False" l="0" tags="">
      </txt>
      <txt j="3102" x="256" y="280" w="144" h="23" t="Status" wrap="False" l="0" tags="">
      </txt>
      <txt j="3103" x="1286" y="881" w="144" h="23" t="Status" wrap="False" l="0" tags="">
      </txt>
      <txt j="3104" x="1286" y="1002" w="144" h="23" t="Status" wrap="False" l="0" tags="">
      </txt>
      <btn j="3201" x="445" y="84" w="210" h="124" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">Aan</inactive>
        <active s="0">Aan</active>
      </btn>
      <btn j="3202" x="445" y="204" w="210" h="124" t="Button_aan_uit_INV_1" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="1" l="0" s="1" tags="">
        <inactive s="0">Uit</inactive>
        <active s="0">Uit</active>
      </btn>
      <txt j="0" x="1066" y="710" w="141" h="87" t="subTitle" wrap="False" l="0" tags="" overrideFontAlignmentH="center" overrideFontAlignmentV="center">Beamer
Links</txt>
      <txt j="0" x="1286" y="710" w="141" h="87" t="subTitle" wrap="False" l="0" tags="" overrideFontAlignmentH="center" overrideFontAlignmentV="center">Beamer
Rechts</txt>
    </landscape>
  </page>
  <subpage name="navigatie" folder="" w="270" h="1050" clip="1">
    <btn j="1" x="10" y="10" w="244" h="71" t="Buttonzonderplaatje" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="settings" sim="0" l="0" s="1" tags="" js="CF.setJoin(&quot;NAVIGATIE&quot; , 0 ); CF.setJoin(&quot;d31&quot; , 1 ); ">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <btn j="310" x="34" y="106" w="197" h="129" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="NAVIGATIE">
      <inactive s="0">Camera
Bediening</inactive>
      <active s="0">Camera
Bediening</active>
    </btn>
    <btn j="320" x="34" y="246" w="197" h="129" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="NAVIGATIE">
      <inactive s="0">Geluid
Bediening</inactive>
      <active s="0">Geluid
Bediening</active>
    </btn>
    <btn j="330" x="34" y="396" w="197" h="129" t="Button_aan_uit_INV" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="NAVIGATIE">
      <inactive s="0">Schermen
Bediening</inactive>
      <active s="0">Schermen
Bediening</active>
    </btn>
  </subpage>
  <subpage name="MicStatus" folder="" w="1080" h="130" clip="1">
    <img x="662" y="5" w="408" h="120" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
    <img x="273" y="5" w="153" h="120" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
    <btn j="141" x="290" y="2" w="30" h="37" t="box" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PAGE2">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <txt j="141" x="327" y="9" w="50" h="20" t="TickBoxText" wrap="False" l="0" tags="">-99</txt>
    <btn j="151" x="290" y="47" w="30" h="37" t="box" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PAGE2">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <txt j="151" x="327" y="55" w="50" h="20" t="TickBoxText" wrap="False" l="0" tags="">-99</txt>
    <btn j="161" x="290" y="91" w="30" h="37" t="box" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PAGE2">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <txt j="161" x="327" y="99" w="50" h="20" t="TickBoxText" wrap="False" l="0" tags="">-99</txt>
    <txt j="0" x="371" y="10" w="80" h="20" t="TickBoxText" wrap="False" l="0" tags="">Zaal 1</txt>
    <txt j="0" x="371" y="55" w="80" h="20" t="TickBoxText" wrap="False" l="0" tags="">Zaal 2</txt>
    <txt j="0" x="371" y="99" w="74" h="20" t="TickBoxText" wrap="False" l="0" tags="">Zaal 3</txt>
    <btn j="142" x="720" y="1" w="30" h="37" t="box" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PAGE2">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <txt j="142" x="757" y="9" w="50" h="20" t="TickBoxText" wrap="False" l="0" tags="">-99</txt>
    <btn j="152" x="720" y="47" w="30" h="37" t="box" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PAGE2">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <txt j="152" x="757" y="55" w="50" h="20" t="TickBoxText" wrap="False" l="0" tags="">-99</txt>
    <btn j="162" x="720" y="91" w="30" h="37" t="box" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PAGE2">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <txt j="162" x="757" y="99" w="50" h="20" t="TickBoxText" wrap="False" l="0" tags="">-99</txt>
    <btn j="143" x="840" y="1" w="30" h="37" t="box" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PAGE2">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <txt j="143" x="877" y="9" w="50" h="20" t="TickBoxText" wrap="False" l="0" tags="">-99</txt>
    <btn j="153" x="840" y="47" w="30" h="37" t="box" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PAGE2">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <txt j="153" x="877" y="55" w="50" h="20" t="TickBoxText" wrap="False" l="0" tags="">-99</txt>
    <txt j="163" x="877" y="99" w="50" h="20" t="TickBoxText" wrap="False" l="0" tags="">-99</txt>
    <btn j="144" x="960" y="1" w="30" h="37" t="box" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PAGE2">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <txt j="144" x="997" y="9" w="50" h="20" t="TickBoxText" wrap="False" l="0" tags="">-99</txt>
    <btn j="154" x="960" y="47" w="30" h="37" t="box" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PAGE2">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <txt j="154" x="997" y="55" w="50" h="20" t="TickBoxText" wrap="False" l="0" tags="">-99</txt>
    <txt j="164" x="997" y="99" w="50" h="20" t="TickBoxText" wrap="False" l="0" tags="">-99</txt>
    <btn j="163" x="840" y="91" w="30" h="37" t="box" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PAGE2">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <btn j="164" x="960" y="91" w="30" h="37" t="box" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="PAGE2">
      <inactive s="0">
      </inactive>
      <active s="0">
      </active>
    </btn>
    <txt j="30" x="0" y="90" w="314" h="40" t="Title" wrap="False" l="0" tags="" overrideFontAlignmentH="left" overrideFontAlignmentV="center">    Bediening?</txt>
  </subpage>
  <subpage name="list" folder="" w="400" h="20" clip="1">
    <txt j="20" x="0" y="0" w="320" h="20" t="logList" wrap="False" l="0" tags="">New Text Object</txt>
    <txt j="21" x="320" y="0" w="80" h="20" t="logList" wrap="False" l="0" tags="">New Text Object</txt>
  </subpage>
  <subpage name="listPage" folder="" w="400" h="800" clip="1">
    <list j="1" x="0" y="0" w="400" h="800" headerSub="" titleSub="" contentSub="list" footerSub="" orientation="v" l="0" swipedelete="0" tags="" />
  </subpage>
</gui>