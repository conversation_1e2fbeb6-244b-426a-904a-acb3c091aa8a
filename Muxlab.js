/*  TCP Link module for CommandFusion (iViewer)
Project:      MuxLab 4x4 Matrix
Programmer:   <Your Name>
Description:  <PERSON><PERSON><PERSON> for controlling MuxLab 500412-V2 via TCP with a sturdy state machine
Version:      v1.2
Date:         <Date>
--------------------------------------------------------------------------------*/

var muxlab = {
    //--------------------------------------------------------------------
    // 1) UI / Join Configuration
    //--------------------------------------------------------------------
    routeButtons: [
        {join: "d11", in: 1, out: 1}, {join: "d12", in: 2, out: 1}, {join: "d13", in: 3, out: 1}, {join: "d14", in: 4, out: 1},
        {join: "d21", in: 1, out: 2}, {join: "d22", in: 2, out: 2}, {join: "d23", in: 3, out: 2}, {join: "d24", in: 4, out: 2},
        {join: "d31", in: 1, out: 3}, {join: "d32", in: 2, out: 3}, {join: "d33", in: 3, out: 3}, {join: "d34", in: 4, out: 3},
        {join: "d41", in: 1, out: 4}, {join: "d42", in: 2, out: 4}, {join: "d43", in: 3, out: 4}, {join: "d44", in: 4, out: 4},
    ],
    presetButtons: [
        // "d101",  // e.g. “Preset 1”
        // "d102",  // e.g. “Preset 2”
    ],
    currentRouting: [0, 0, 0, 0, 0],

    //--------------------------------------------------------------------
    // 2) System Name, Buffer, and State Machine Variables
    //--------------------------------------------------------------------
    systemName: "MUXLAB-TCPLINK",
    feedbackName: "raw_MUXLAB-TCPLINK",
    buffer: [],
    sendInterval: null,
    connStates: {
        IDLE: 'IDLE',
        KICK: 'KICK',
        OK: 'OK',
        TIMEOUT: 'TIMEOUT'
    },
    connState: 'IDLE',
    connTimeout: null,

    //--------------------------------------------------------------------
    // 3) Initialization
    //--------------------------------------------------------------------
    init: function() {
        CF.log("Init MuxLab 4x4 Matrix Script");

        // Watch device feedback
        CF.watch(CF.FeedbackMatchedEvent, muxlab.systemName, muxlab.feedbackName, muxlab.parseData);

        // Watch route buttons
        muxlab.routeButtons.forEach(function(b) {
            CF.watch(CF.ObjectPressedEvent, b.join, muxlab.onRoutePress);
        });

        // Watch preset buttons
        CF.watch(CF.ObjectPressedEvent, muxlab.presetButtons, muxlab.onPresetPress);

        // Watch connection status
        CF.watch(CF.ConnectionStatusChangeEvent, muxlab.systemName, muxlab.onConnectionChange);

        // Start command sending interval
        muxlab.sendInterval = setInterval(muxlab.sendBuffer, 100);

        // Start the state machine
        muxlab.connect(muxlab.connStates.IDLE);
    },

    //--------------------------------------------------------------------
    // 4) Connection Handling
    //--------------------------------------------------------------------
    onConnectionChange: function(system, connected, remote) {
        if (connected) {
            CF.log("MuxLab connected: System " + system + " with " + remote);
            CF.setJoin("d10", 1); // Set connected LED
            CF.setJoin("s10", "Connected: " + remote);
            setTimeout(() => {
                muxlab.send("r status!");
                CF.flipToPage("Matrix");
                CF.setJoin("d330", 1);
            }, 8000);
        } else {
            CF.log("MuxLab disconnected");
            CF.setJoin("d10", 0); // Clear connected LED
            CF.setJoin("s10", "Disconnected");
            // Let the state machine handle reconnection via TIMEOUT
        }
    },

    connect: function(immediateState) {
        if (immediateState) {
            muxlab.connState = immediateState;
        }

        switch (muxlab.connState) {
            case muxlab.connStates.IDLE:
                CF.setJoin("s1", "IDLE"); // Optional: for debugging
                muxlab.connState = muxlab.connStates.KICK;
                muxlab.connTimeout = setTimeout(muxlab.connect, 3000);
                break;

            case muxlab.connStates.OK:
                CF.setJoin("s1", "OK");
                clearTimeout(muxlab.connTimeout);
                muxlab.connState = muxlab.connStates.KICK;
                muxlab.connTimeout = setTimeout(muxlab.connect, 3000);
                break;

            case muxlab.connStates.KICK:
                CF.setJoin("s1", "KICK");
                muxlab.connState = muxlab.connStates.TIMEOUT;
                muxlab.send("r status!");
                clearTimeout(muxlab.connTimeout);
                muxlab.connTimeout = setTimeout(muxlab.connect, 1000);
                break;

            case muxlab.connStates.TIMEOUT:
                CF.setJoin("s1", "TIMEOUT");
                CF.log("MuxLab connection TIMEOUT");
                // Attempt reconnection
                CF.setSystemProperties(muxlab.systemName, { enabled: false });
                setTimeout(function() {
                    CF.setSystemProperties(muxlab.systemName, { enabled: true });
                }, 100); // Small delay to ensure disable takes effect
                clearTimeout(muxlab.connTimeout);
                muxlab.connTimeout = setTimeout(muxlab.connect, 3000);
                break;
        }
    },

    //--------------------------------------------------------------------
    // 5) Route & Preset Handling
    //--------------------------------------------------------------------
    onRoutePress: function(join, value, tokens, tags) {
        var btn = muxlab.routeButtons.find(function(b) {
            return b.join === join;
        });
        if (!btn) return;

        var cmd = "s in " + btn.in + " av out " + btn.out + "!";
        muxlab.send(cmd);
        CF.log("Routing Input " + btn.in + " to Output " + btn.out);
    },

    onPresetPress: function(join, value, tokens, tags) {
        switch (join) {
            case "d101":
                muxlab.send("s in 1 av out 1!");
                muxlab.send("s in 2 av out 2!");
                muxlab.send("s in 3 av out 3!");
                muxlab.send("s in 4 av out 4!");
                CF.log("Preset #1 pressed: each output matched to same input #");
                break;
            case "d102":
                muxlab.send("s in 4 av out 1!");
                muxlab.send("s in 3 av out 2!");
                muxlab.send("s in 2 av out 3!");
                muxlab.send("s in 1 av out 4!");
                CF.log("Preset #2 pressed: cross-swapped routing");
                break;
        }
    },

    //--------------------------------------------------------------------
    // 6) Parsing Feedback & Updating Joins
    //--------------------------------------------------------------------
    parseData: function(fbName, rawData) {
        // Any feedback indicates the device is responsive
        muxlab.connect(muxlab.connStates.OK);

        CF.log("MuxLab Feedback: " + rawData);

        var matchRouting = rawData.match(/input\s+(\d+)\s*->\s*output\s+(\d+)/i);
        if (matchRouting) {
            var inputNum = parseInt(matchRouting[1], 10);
            var outputNum = parseInt(matchRouting[2], 10);

            // Update UI joins
            muxlab.routeButtons.forEach(function(b) {
                if (b.out === outputNum) {
                    CF.setJoin(b.join, b.in === inputNum ? 1 : 0);
                }
            });

            // Track current routing
            muxlab.currentRouting[outputNum] = inputNum;
        }
    },

    //--------------------------------------------------------------------
    // 7) Sending Commands Via Buffer
    //--------------------------------------------------------------------
    send: function(cmd) {
        muxlab.buffer.push(cmd);
        CF.log("Queue Command: " + cmd);
    },

    sendBuffer: function() {
        if (muxlab.buffer.length > 0) {
            var command = muxlab.buffer.shift();
            CF.send(muxlab.systemName, command + "\r");
        }
    }
};

CF.modules.push({ name: "MuxLab 4x4 Controller", setup: muxlab.init });