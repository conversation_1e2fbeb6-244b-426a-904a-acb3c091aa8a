/*
================================================================================
Project:       Sharp AQUOS TV Control via IP
Programmer:    Adapted from Optoma script by Grok for Sharp TV
Info:          Controls Sharp AQUOS TV (LC-60LE635E series compatible) via IP.
               Handles Authentication, Power, Mute, and Status Feedback.
Version:       v1.0
Date:          October 26, 2023
Client:        [Your Name/Project]
Requires:      TCP Client System named "SharpTV" in CF System Manager,
               configured with TV IP Address and Port 10002.
               Feedback delimiter set to \x0a (0D hex).
================================================================================
*/

var sharpTV = {
    // --- Configuration ---
    systemName: "SharpTV",          // System name in CommandFusion GUI
    feedbackName: "raw_SharpTV",    // Feedback name matching systemName
    queryTimeout: 5000,             // Timeout for status queries (milliseconds)
    queryInterval: 15000,           // Interval between full status query cycles (milliseconds)
    connectionRetryInterval: 10000, // Interval to retry connection if disconnected
    username: "admin",             // Login username
    password: "admin",             // Login password

    // --- State Variables ---
    connected: false,
    commandQueue: [],
    isSending: false,               // Flag to prevent command overlap
    queryTimeoutId: null,
    queryIntervalId: null,
    connectionRetryTimer: null,
    lastKnownPowerState: -1,        // -1: unknown, 0: off, 1: on
    lastKnownMuteState: -1,         // -1: unknown, 0/2: off, 1: on

    // --- Joins (Update these to match your GUI file) ---
    powerOnJoin: "d3201",           // Power On button / feedback
    powerOffJoin: "d3202",          // Power Off button / feedback
    muteOnJoin: "d3203",            // Mute On button / feedback
    muteOffJoin: "d3204",           // Mute Off button / feedback
    connectionDJoin: "d113",        // Connection Status Digital Indicator
    connectionSJoin: "s113",        // Connection Status Serial Info Text
    powerOnCountdownJoin: "s3201",  // Power On button countdown text
    powerOffCountdownJoin: "s3202", // Power Off button countdown text
    muteOnCountdownJoin: "s3203",   // Mute On button countdown text
    muteOffCountdownJoin: "s3204",  // Mute Off button countdown text

    // --- Sharp Commands (Formatted) ---
    cmdPowerOn: "POWR1   \x0d",     // Commands must be exactly 8 chars + CR
    cmdPowerOff: "POWR0   \x0d",    // Using \x0d (CR) for line endings
    cmdMuteOn: "MUTE1   \x0d",      // Padding with spaces to 8 chars
    cmdMuteOff: "MUTE2   \x0d",     // All commands padded to 8 chars
    cmdPowerQuery: "POWR?   \x0d",  // Query commands follow same format
    cmdMuteQuery: "MUTE?   \x0d",   // Using single CR (\x0d) ending

    // --- Countdown Timer Tracking ---
    countdownTimers: {},

    // --- Initialization ---
    init: function () {
        CF.log("Sharp TV Control Initializing...");
        sharpTV.loginState = 0; // Start as disconnected

        // Watch GUI button presses
        CF.watch(CF.ObjectPressedEvent, [sharpTV.powerOnJoin, sharpTV.powerOffJoin], sharpTV.powerPressed);
        CF.watch(CF.ObjectPressedEvent, [sharpTV.muteOnJoin, sharpTV.muteOffJoin], sharpTV.mutePressed);

        // Watch for feedback from the TV
        CF.watch(CF.FeedbackMatchedEvent, sharpTV.systemName, sharpTV.feedbackName, sharpTV.parseData);

        // Watch for connection status changes
        CF.watch(CF.ConnectionStatusChangeEvent, sharpTV.systemName, sharpTV.onConnectionChange, true);

        sharpTV.restoreStates(); // Restore last known UI state
        sharpTV.updateConnectionFeedback(); // Update UI based on initial state
        // Query loop will start automatically upon successful connection and authentication
    },

    // --- Helper Functions ---
    // Pads command parameters to 4 characters with spaces
    padParam: function (param) {
        param = String(param); // Ensure it's a string
        while (param.length < 4) {
            param += " ";
        }
        return param.substring(0, 4); // Ensure exactly 4 chars
    },

    // Formats a command and parameter correctly
    formatCommand: function (command, parameter) {
        // Ensure command is 4 chars, parameter is 4 chars (padded)
        var cmdPart = command.substring(0, 4);
        while (cmdPart.length < 4) { cmdPart += " "; } // Should not be needed if cmd names are correct

        var paramPart = sharpTV.padParam(parameter);
        return cmdPart + paramPart + "\x0d"; // Using CR (\x0d) for line endings
    },

    // --- Communication ---
    sendNextCommand: function() {
        if (sharpTV.isSending || sharpTV.commandQueue.length === 0 || sharpTV.loginState !== 4) {
            return; // Don't send if busy, queue empty, or not authenticated
        }

        sharpTV.isSending = true;
        var item = sharpTV.commandQueue.shift(); // Get the next command from the queue

        CF.log("SharpTV Sending: " + item.command.replace('\x0a', '\\x0a'));
        CF.send(sharpTV.systemName, item.command);

        // Set timeout for query commands
        if (item.isQuery) {
            clearTimeout(sharpTV.queryTimeoutId); // Clear previous timeout
            sharpTV.queryTimeoutId = setTimeout(function () {
                CF.log("SharpTV Query Timeout for: " + item.command.replace('\x0a', '\\x0a'));
                sharpTV.isSending = false;
                sharpTV.sendNextCommand(); // Try next command in queue
            }, sharpTV.queryTimeout);
        } else {
             // For non-query commands, allow sending next command sooner
             // Introduce a small delay to avoid overwhelming the TV
            setTimeout(function() {
                sharpTV.isSending = false;
                sharpTV.sendNextCommand();
            }, 250); // 250ms delay between sending commands
        }
    },

    // Add command to the queue
    queueCommand: function(command, isQuery) {
         if (sharpTV.loginState !== 4) {
             CF.log("SharpTV WARN: Not authenticated, cannot queue command: " + command.replace('\x0a', '\\x0a'));
             return;
         }
        sharpTV.commandQueue.push({ command: command, isQuery: isQuery });
        sharpTV.sendNextCommand(); // Attempt to send immediately if possible
    },

    // --- Status Querying ---
    startQueryLoop: function () {
        sharpTV.stopQueryLoop(); // Stop existing loop if any
        if (sharpTV.loginState !== 4) {
            CF.log("SharpTV: Cannot start query loop, not authenticated.");
            return;
        }
        CF.log("SharpTV: Starting query loop.");
        sharpTV.queryDeviceStatus(); // Query immediately
        sharpTV.queryIntervalId = setInterval(sharpTV.queryDeviceStatus, sharpTV.queryInterval);
    },

    stopQueryLoop: function () {
        if (sharpTV.queryIntervalId) {
            CF.log("SharpTV: Stopping query loop.");
            clearInterval(sharpTV.queryIntervalId);
            sharpTV.queryIntervalId = null;
        }
         clearTimeout(sharpTV.queryTimeoutId); // Also clear any pending query timeout
    },

    queryDeviceStatus: function () {
        if (sharpTV.loginState !== 4 || sharpTV.isSending) {
            return; // Don't query if not ready or already sending
        }
        CF.log("SharpTV: Queuing status query...");
        // Queue power query first
        sharpTV.lastQuery = "POWR?";  // Track what we're querying
        sharpTV.queueCommand(sharpTV.formatCommand("POWR", "?"), true);
        // Mute query will be triggered by the power query response handler
    },

    // --- Feedback Parsing ---
    parseData: function (fbName, rawData) {
        // Remove any trailing line endings for consistent comparison
        var cleanData = rawData.replace(/[\r\n]+$/, '');
        CF.log("SharpTV Raw Data Received: '" + rawData.split('').map(c => c.charCodeAt(0)).join(',') + "'");
        
        // Handle Authentication Flow
        if (sharpTV.loginState <= 1 && cleanData.indexOf("Password:") !== -1) {
            CF.log("SharpTV: Password prompt detected, sending password...");
            sharpTV.loginState = 2;
            // Send password with just LF
            CF.send(sharpTV.systemName, sharpTV.password + "\x0a");
            CF.log("SharpTV Sending: [password]");
            sharpTV.loginState = 3;
            return;
        }

        // Check for authentication failure
        if (cleanData.indexOf("User Name or Password mismatch") !== -1) {
            CF.log("SharpTV: Authentication failed, retrying...");
            sharpTV.loginState = 1; // Reset to username state
            setTimeout(function() {
                // Retry with username
                CF.send(sharpTV.systemName, sharpTV.username + "\x0a");
                CF.log("SharpTV Retrying username: " + sharpTV.username);
            }, 1000);
            return;
        }

        // Handle successful authentication response
        if (sharpTV.loginState === 3 && cleanData === "") {
            CF.log("SharpTV: Authentication successful.");
            sharpTV.loginState = 4;
            sharpTV.updateConnectionFeedback();
            sharpTV.isSending = false;  // Ready to send commands now
            sharpTV.commandQueue = []; // Clear any commands queued before auth
            clearTimeout(sharpTV.queryTimeoutId); // Clear any timeouts from failed auth attempts
            sharpTV.startQueryLoop(); // Start polling status
            sharpTV.sendNextCommand(); // Send any commands queued after this state change
            return;
        }

        // Now clean the data for command processing - IMPORTANT: Don't trim yet as we need exact response
        var responseData = cleanData;
        
        // Handle Command/Query Responses (Only if Authenticated)
        if (sharpTV.loginState === 4) {
             // Clear query timeout if we received a response relevant to a query
             if (sharpTV.isSending && sharpTV.commandQueue.length > 0 && sharpTV.commandQueue[0].isQuery) {
                 // Basic check: if we are expecting a query response and get OK/ERR or a value
                 if (responseData === "OK" || responseData === "ERR" || !isNaN(parseInt(responseData))) {
                      clearTimeout(sharpTV.queryTimeoutId);
                 }
             }

             if (responseData === "OK") {
                 CF.log("SharpTV: Command successful (OK).");
                 sharpTV.isSending = false; // Ready for next command
                 sharpTV.sendNextCommand();
             } else if (responseData === "ERR") {
                 CF.log("SharpTV ERROR: Command failed (ERR).");
                 sharpTV.isSending = false; // Ready for next command (maybe clear queue?)
                 sharpTV.sendNextCommand();
             } else if (responseData.length > 0) { // Potentially a query response
                 // Determine which query this response belongs to (simplistic: assumes response follows query)
                 var currentCommand = sharpTV.isSending ? sharpTV.commandQueue[0]?.command : null;

                 if (currentCommand) {
                     if (currentCommand.indexOf("POWR?") !== -1) {
                         // Don't trim here - take the exact response
                         var powerState = parseInt(responseData);
                         CF.log("SharpTV Raw Power State Response: '" + responseData + "', Parsed State: " + powerState);
                         if (!isNaN(powerState)) {
                             CF.log("SharpTV Status: Power is " + (powerState === 1 ? "On" : "Off"));
                             // Update UI immediately
                             sharpTV.updatePowerFeedback(powerState);
                             sharpTV.lastKnownPowerState = powerState;
                             sharpTV.saveStates();
                             // Now queue the mute query
                             sharpTV.isSending = false; // Mark power query as done
                             sharpTV.queueCommand(sharpTV.formatCommand("MUTE", "?"), true);
                         } else { 
                             CF.log("SharpTV WARN: Unexpected power query response: " + responseData); 
                             sharpTV.isSending = false; 
                         }

                     } else if (currentCommand.indexOf("MUTE?") !== -1) {
                         var muteState = parseInt(responseData);
                          if (!isNaN(muteState)) {
                             CF.log("SharpTV Status: Mute is " + muteState + (muteState === 1 ? " (On)" : " (Off)"));
                             sharpTV.updateMuteFeedback(muteState);
                             sharpTV.lastKnownMuteState = muteState;
                             sharpTV.saveStates();
                             sharpTV.isSending = false; // Mark mute query as done
                         } else { 
                             CF.log("SharpTV WARN: Unexpected mute query response: " + responseData); 
                             sharpTV.isSending = false; 
                         }
                         sharpTV.sendNextCommand(); // Check queue after finishing query cycle
                     } else {
                          CF.log("SharpTV WARN: Received response '" + responseData + "' but didn't match expected query.");
                          sharpTV.isSending = false; // Allow next command attempt
                          sharpTV.sendNextCommand();
                     }
                 } else {
                      CF.log("SharpTV WARN: Received response '" + responseData + "' but no command was marked as sending.");
                      // Could be unsolicited status update, handle accordingly
                      // Check if it's a power or mute status
                      if (responseData.length === 1) {
                          var state = parseInt(responseData);
                          if (!isNaN(state)) {
                              // Try to determine if this is power or mute based on previous queries
                              if (sharpTV.lastQuery === "POWR?") {
                                  sharpTV.updatePowerFeedback(state);
                                  sharpTV.lastKnownPowerState = state;
                                  sharpTV.saveStates();
                              } else if (sharpTV.lastQuery === "MUTE?") {
                                  sharpTV.updateMuteFeedback(state);
                                  sharpTV.lastKnownMuteState = state;
                                  sharpTV.saveStates();
                              }
                          }
                      }
                 }
             } else {
                  CF.log("SharpTV WARN: Received empty or unhandled data.");
                  sharpTV.isSending = false; // Prevent getting stuck
                  sharpTV.sendNextCommand();
             }
        } else {
             CF.log("SharpTV WARN: Received data while not authenticated: '" + responseData + "'");
        }
    },

    // --- UI Feedback Updates ---
    updatePowerFeedback: function(state) { // state: 0=off, 1=on
        CF.log("SharpTV Setting Power Feedback - State: " + state + ", PowerOn Join: " + sharpTV.powerOnJoin + ", PowerOff Join: " + sharpTV.powerOffJoin);
        CF.setJoin(sharpTV.powerOnJoin, state === 1 ? 1 : 0);
        CF.setJoin(sharpTV.powerOffJoin, state === 0 ? 1 : 0);
        CF.log("SharpTV Power Feedback Set - PowerOn(" + sharpTV.powerOnJoin + ")=" + (state === 1 ? 1 : 0) + ", PowerOff(" + sharpTV.powerOffJoin + ")=" + (state === 0 ? 1 : 0));
    },

    updateMuteFeedback: function(state) { // state: 0=toggle/off, 1=on, 2=off
        CF.setJoin(sharpTV.muteOnJoin, state === 1 ? 1 : 0);
        CF.setJoin(sharpTV.muteOffJoin, state !== 1 ? 1 : 0); // Mute off if state is 0 or 2
    },

    updateConnectionFeedback: function() {
        var connected = (sharpTV.loginState > 0);
        var statusText = "Disconnected";
        if (connected) {
            if (sharpTV.loginState < 4) {
                statusText = "Connecting...";
            } else {
                statusText = "Connected"; // Could add IP here if needed CF.getSystemProperties(sharpTV.systemName)...
            }
        }

        CF.setJoin(sharpTV.connectionDJoin, connected ? 1 : 0);
        CF.setJoin(sharpTV.connectionSJoin, statusText);

        // Reset UI buttons if disconnected
        if (!connected) {
            sharpTV.updatePowerFeedback(-1); // Show both off
            sharpTV.updateMuteFeedback(-1);  // Show both off
        }
    },


    // --- Connection Handling ---
    onConnectionChange: function (system, connected, remote) {
        clearTimeout(sharpTV.connectionRetryTimer); // Stop trying to reconnect if status changes
        sharpTV.stopQueryLoop(); // Stop polling on any change
        sharpTV.commandQueue = []; // Clear queue on change
        sharpTV.isSending = false; // Reset sending flag
        clearTimeout(sharpTV.queryTimeoutId);

        if (connected) {
            CF.log("SharpTV Connected to: " + remote);
            sharpTV.loginState = 1; // Set state to connected, need to authenticate
            sharpTV.updateConnectionFeedback();
            // Add small delay before sending username to ensure connection is stable
            setTimeout(function() {
                CF.send(sharpTV.systemName, sharpTV.username + "\x0a");
                CF.log("SharpTV Sending username: " + sharpTV.username);
            }, 1000);
            // The parseData function will handle the password prompt -> password sending -> OK confirmation
        } else {
            CF.log("SharpTV Disconnected.");
            sharpTV.loginState = 0;
            sharpTV.updateConnectionFeedback();
            // Attempt to reconnect after a delay
            sharpTV.connectionRetryTimer = setTimeout(function() {
                CF.log("SharpTV: Retrying connection...");
                CF.startConnection(sharpTV.systemName);
            }, sharpTV.connectionRetryInterval);
        }
    },

    // --- Button Press Handlers ---
    powerPressed: function(join, value, tokens, tags) {
        var command = "";
        var targetState = -1; // 0 for Off, 1 for On

        if (join === sharpTV.powerOnJoin) {
            command = sharpTV.cmdPowerOn;
            targetState = 1;
        } else if (join === sharpTV.powerOffJoin) {
            command = sharpTV.cmdPowerOff;
            targetState = 0;
        }

        if (command && sharpTV.loginState === 4) {
            // Optional: Check if already in target state to avoid redundant commands
            // if (sharpTV.lastKnownPowerState === targetState) {
            //     CF.log("SharpTV: Power already in target state (" + targetState + "). Ignoring command.");
            //     return;
            // }
            CF.log("SharpTV Queuing Power Command: " + command.replace('\x0a', '\\x0a'));
            sharpTV.queueCommand(command, false); // Queue the command

            // Start countdown timer
            var textJoin = (join === sharpTV.powerOnJoin) ? sharpTV.powerOnCountdownJoin : sharpTV.powerOffCountdownJoin;
            sharpTV.startCountdown(textJoin, (targetState === 1 ? 20 : 10)); // Longer countdown for On? Adjust as needed
        } else if (sharpTV.loginState !== 4) {
             CF.log("SharpTV ERROR: Cannot send power command, not authenticated.");
        }
    },

    mutePressed: function(join, value, tokens, tags) {
        var command = "";
        var targetState = -1; // 1 for Mute On, 0/2 for Mute Off

        if (join === sharpTV.muteOnJoin) {
            command = sharpTV.cmdMuteOn;
             targetState = 1;
        } else if (join === sharpTV.muteOffJoin) {
            command = sharpTV.cmdMuteOff;
             targetState = 2; // Target off state
        }

        if (command && sharpTV.loginState === 4) {
            // Optional: Check if already in target state
            // Note: Mute state check is slightly complex (0 or 2 means off)
            // if ((targetState === 1 && sharpTV.lastKnownMuteState === 1) || (targetState === 2 && sharpTV.lastKnownMuteState !== 1)) {
            //     CF.log("SharpTV: Mute already in target state. Ignoring command.");
            //     return;
            // }
             CF.log("SharpTV Queuing Mute Command: " + command.replace('\x0a', '\\x0a'));
             sharpTV.queueCommand(command, false);

            // Start countdown timer
            var textJoin = (join === sharpTV.muteOnJoin) ? sharpTV.muteOnCountdownJoin : sharpTV.muteOffCountdownJoin;
            sharpTV.startCountdown(textJoin, 4); // 4 second countdown for mute
        } else if (sharpTV.loginState !== 4) {
            CF.log("SharpTV ERROR: Cannot send mute command, not authenticated.");
        }
    },

    // --- Countdown Timer ---
    startCountdown: function(textJoin, duration) {
        if (!textJoin) return;

        // Stop any existing countdown for this text join
        if (sharpTV.countdownTimers[textJoin]) {
            clearInterval(sharpTV.countdownTimers[textJoin]);
        }

        var count = duration;
        CF.setJoin(textJoin, String(count)); // Display initial value

        var intervalId = setInterval(function() {
            count--;
            if (count >= 0) {
                CF.setJoin(textJoin, String(count)); // Update countdown
            } else {
                clearInterval(intervalId);
                delete sharpTV.countdownTimers[textJoin];
                CF.setJoin(textJoin, ""); // Clear text when done
            }
        }, 1000); // Update every second

        sharpTV.countdownTimers[textJoin] = intervalId; // Store timer ID
    },

    // --- State Persistence ---
    restoreStates: function () {
        CF.getProperties({ keys: ["sharpTVState"] }, function (items) {
            if (items && items.sharpTVState) {
                try {
                    var storedStates = JSON.parse(items.sharpTVState);
                    sharpTV.lastKnownPowerState = storedStates.power;
                    sharpTV.lastKnownMuteState = storedStates.mute;
                    sharpTV.updatePowerFeedback(storedStates.power);
                    sharpTV.updateMuteFeedback(storedStates.mute);
                    CF.log("SharpTV Restored states: " + JSON.stringify(storedStates));
                } catch (e) {
                    CF.log("SharpTV ERROR: Failed to parse stored state: " + e);
                    sharpTV.updatePowerFeedback(-1); // Reset UI if parse fails
                    sharpTV.updateMuteFeedback(-1);
                }
            } else {
                CF.log("SharpTV: No previous state found.");
                sharpTV.updatePowerFeedback(-1); // Ensure UI starts in unknown state
                sharpTV.updateMuteFeedback(-1);
            }
        });
    },

    saveStates: function () {
        // Only save if we have valid known states
        if (sharpTV.lastKnownPowerState !== -1 && sharpTV.lastKnownMuteState !== -1) {
            var states = {
                power: sharpTV.lastKnownPowerState,
                mute: sharpTV.lastKnownMuteState
            };
            CF.setProperties({ sharpTVState: JSON.stringify(states) }, function() {
                 // Optional callback on success
                 CF.log("SharpTV Saved states: " + JSON.stringify(states));
            }, function (error) {
                 // Optional callback on error
                 CF.log("SharpTV ERROR: Failed to save state: " + error);
            });
        }
    }
};

// Register the module with CommandFusion
CF.modules.push({
    name: "Sharp AQUOS TV Module",
    setup: sharpTV.init // Call init function on startup
    // You might add other lifecycle methods if needed (e.g., deinit)
});