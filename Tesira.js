/*  Tesira control Tesira Forté AI
Project:      	CGK Urk
Programmeur:	<PERSON>
info:       	<EMAIL>	
versie:      	v1.0
datum:        	Augustus 2018
gebaseerd op:   Denon.js (<PERSON>)  Rients Faber/project PG Ruurlo
=====================================================================================================
Omschrijving:
CommandFusion module voor het aansturen van een tesira Forté AI via IP!

HG Bruinisse 2020-01-29

2021-02-24 - PKO op verzoek van de klant
thv regel 350, mutes
als afkondigen is actief, speakers gemute,
lipmicrofoon aan schakelen.
als afkondigen uitgezet wordt, lipmicrofoon weer muten.

PKO 2022-01-05 aangepast naar Woerden GG Zalen
*/
//var tempJoin = "";
//var TempResponse = "";
//var NoConnection = true;

//	CF.setSystemProperties(tesira.systemName,{enabled: false});
//	setTimeout(function(){CF.setSystemProperties(tesira.systemName,{enabled: true});},5000);



var tesira = {

		buffer 				: [],
		sliders				: [ "a101", "a111", "a121", "a122", "a123", "a131", "a132", "a141", "a142", "a143", "a144"],
		
		slidersMusic 		: [], 
		MicMuteButtons		: [ "d101", "d111", "d121", "d122", "d123", "d131", "d132", "d141", "d142", "d143", "d144", "d151"],

		LogicMeterButtons	: [ "d20" , "d21" , "d22" , "d23" , "d24" , "d25" , "d26" , "d27" ],

		muteButtons 		: ["d30"],

		interval 			: 500, // 1 s
		poll 				: null,
        timerId         	: null,
        timerIdResterend	: "",
        systemName      	: "TesiraServer",
        feedbackName    	: "Tesira_raw",
        minvaluemic			: -12,		// MIN waarde van de slider Microfoon in GUI
        rangevaluemic		: 24,		// Range van MIN naar MAX van slider Microfoon in GUI
        minvaluemusic		: -30,		// Min waarde van de slider Muziek in GUI
        rangevaluemusic		: 38,		// Range van MIN naar MAX van slider Muziek in GUI
        //mictotaal			: "a11",	// analog join slider mic totaal
        //musictotaal		: "a21",	// analog join slider music totaal

        tempJoin			: [],
        NoConnection 		: true,

        receiveBuffer		: [],

        connState			: connStates.IDLE,
		  connTimeout			: 0,



    init : function (){
    	CF.log("Tesira INIT");
    	CF.watch(CF.FeedbackMatchedEvent, tesira.systemName, tesira.feedbackName, tesira.parseData);             // controller op feedback
    	//setInterval(function(){tesira.sendBuffer();},25)		//Verstuur iedere (100ms) data als deze aanwezig is!
    	CF.watch(CF.ConnectionStatusChangeEvent, tesira.systemName, tesira.onConnectionChange, true);

		CF.watch(CF.ObjectPressedEvent,  tesira.sliders, tesira.onSliderPressed); 	
		CF.watch(CF.ObjectDraggedEvent, tesira.sliders, tesira.onSliderDragged);	
		CF.watch(CF.ObjectReleasedEvent, tesira.sliders, tesira.onSliderReleased);				

		CF.watch(CF.ObjectPressedEvent,  tesira.slidersMusic, tesira.onMusicSliderPressed); 	
		CF.watch(CF.ObjectDraggedEvent, tesira.slidersMusic, tesira.onMusicSliderDragged);	
		CF.watch(CF.ObjectReleasedEvent, tesira.slidersMusic, tesira.onMusicSliderReleased);

		CF.watch(CF.ObjectPressedEvent, tesira.MicMuteButtons, tesira.mutePressed);

		CF.watch(CF.ObjectPressedEvent, tesira.LogicMeterButtons, tesira.logicMeter);
		CF.watch(CF.ObjectPressedEvent, tesira.muteButtons, tesira.mutes);

		tesira.commState = commStates.IDLE
		tesira.connect(connStates.IDLE)
		
		},

    connect : function(immediateState){
 		if (immediateState){
			tesira.connState = immediateState
			//log("immediateState")
		}
		
		switch(tesira.connState){
 			
			case connStates.IDLE:
				CF.setJoin("s1" , "IDLE");
//				log("IDLE")
				tesira.connState = connStates.KICK
				tesira.connTimeout = setTimeout(function(){tesira.connect()}, 3000);
//				log("connStates.IDLE:" + tesira.buffer.length)
				//log(tesira.buffer.length)
				break;
			
			case connStates.OK: //Niets te doen behalve nieuwe poging starten
				CF.setJoin("s1" , "OK");
				//log("OK")
				clearTimeout(tesira.connTimeout)
				tesira.connState = connStates.KICK
				tesira.connTimeout = setTimeout(function(){tesira.connect()}, 3000);
//				CF.log("connStates.OK:")
				break;
			
			case connStates.KICK: //Tijd voor een update
				CF.setJoin("s1" , "KICK");
//				log("KICK")
				tesira.connState = connStates.TIMEOUT
				tesira.send("DEVICE get serialNumber");
				//tesira.Updatebuttons()
				
				clearTimeout(tesira.connTimeout)
				
				tesira.connTimeout = setTimeout(function(){tesira.connect()}, 1000);
//				log("connStates.KICK:")
//				log(tesira.buffer.length)
				break;
			
			case connStates.TIMEOUT: //Geen antwoord
				CF.setJoin("s1" , "TIMEOUT");
				log("Tesira connection TIMEOUT")
				CF.setSystemProperties(tesira.systemName,{enabled: false});
				CF.setSystemProperties(tesira.systemName,{enabled: true});
				clearTimeout(tesira.connTimeout)
				tesira.connTimeout = setTimeout(function(){tesira.connect()}, 3000);
//				log("connStates.TIMEOUT:")
//				log(tesira.buffer.length)
		}
 	},

    onConnectionChange : function(system, connected, remote) {
    	
		if (connected) {
//			CF.log("Tesira Connected");
			log("DSP gevonden, verbinding wordt gemaakt.")
			tesira.makeConnection();
			CF.setJoin("d116",1);
			CF.setJoin("s116" , remote);
    	}
    	else{
    		CF.setJoin("d116",0);
			CF.log("Tesira DISconnected");
			log("Tesira diconnected.");			
			CF.setJoin("s116" , "No connection");
		}
    },

    Updatebuttons : function(){
    	CF.log("Update Sliders");
				



    }, //UpdateButtons

    
    parseData : function(feedbackname,feedbackstring){
    	tesira.connect(connStates.OK)

    	var DEVICE  = feedbackstring.match("DEVICE")
		var LEVEL   = feedbackstring.match("Level")
		var OK 		= feedbackstring.match("OK ")
		var MUTE 	= feedbackstring.match("mute")
		var WELCOME	= feedbackstring.match("\xFF\xFD\x18")
		var LOGICMETER1 = feedbackstring.match("LogicMeter1")
		var LOGICMETER2 = feedbackstring.match("LogicMeter2")
		var CONNECTIONSUCCESFULL = feedbackstring.match("Welcome to the Tesira Text Protocol Server...")
		var MUTE1 = feedbackstring.match("Mute1")

		//toevoeging na subscription:
		var PUBLISHTOKEN = feedbackstring.match("publishToken")

		//AutoSubScribe Levels and Mutes
		var sizeMute  = tesira.MicMuteButtons.length;
		var sizeLevelMic = tesira.sliders.length;
		var sizeLevelMusic = tesira.slidersMusic.length;


//    	CF.log("Received Data from: " + feedbackname + ", Data: " + tesira.makeReadable(null,feedbackstring));



    	if(PUBLISHTOKEN){
    		CF.log("feedbackstring PUBLISHTOKEN: " + feedbackstring)
			var matches = feedbackstring.match(/! "publishToken":"(.*)" "value":(.*)\x0D\x0A/);
//			CF.log("Matches: " + matches)
//			CF.log("Matches[1]" + matches[1])
//			CF.log("Matches[2]" + matches[2])
//			CF.log(matches[1].slice(0,1));

			if(matches[1].slice(0,1) == "a"){
				tesira.tempJoin = matches[1].slice(1);
//				CF.log("analog Join")
				CF.setJoin("a" + tesira.tempJoin, tesira.calculateLevel(matches[2]))
				CF.setJoin("s" + tesira.tempJoin, Math.round(matches[2]))
			}
			
			if(matches[1].slice(0,1) == "d"){

//				CF.log("Digital Join")
				if(matches[2] == "true"){
					CF.setJoin(matches[1],1)

				}
				if(matches[2] == "false"){
					CF.setJoin(matches[1],0)

				}
			}

    	}

		if (WELCOME){	//reactie op 1e bericht van tesira
			CF.log("Tesira.Welcom!!");
		}

		if (CONNECTIONSUCCESFULL){	//Connectie succesvol! mogelijkheid voor het verzenden/ontvangen van data via IP!
			CF.log("Tesira.Conectionl.succesfull");
			log("DSP welkomsbericht, update knoppen")
			tesira.NoConnection = false;

//			setTimeout(function(){	//Subscribe to ID's	
		    	if(sizeLevelMic >= 1){
					for (var i = sizeLevelMic - 1; i >= 0; i--) {
						var instanceTAG = tesira.sliders[i].slice(1,3);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
						var input = tesira.sliders[i].slice(3,4);
						tesira.send("Level" + instanceTAG + " subscribe level " + input + " " + tesira.sliders[i] +" 500"); 	//Level Draadloos 1
					}//for (var i = size - 1; i >= 0; i--) {
		    	}//if(SizeLevel >= 1){
		    	else{ CF.log("sizeLevelMic == 0") }

		    	if(sizeMute >= 1){
					for (var i = sizeMute - 1; i >= 0; i--) {
						var instanceTAG = tesira.MicMuteButtons[i].slice(1,3);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
						var input = tesira.MicMuteButtons[i].slice(3,4);
						tesira.send("Level" + instanceTAG + " subscribe mute " + input + " " + tesira.MicMuteButtons[i] +" 500"); 	//Level Draadloos 1
					}//for (var i = size - 1; i >= 0; i--) {
		    	}//if(SizeLevel >= 1){
		    	else{ CF.log("sizeMute == 0") }

		    	tesira.send("LogicMeter2 subscribe state 1 d20 500");
		    	tesira.send("LogicMeter2 subscribe state 1 d21 500");
		    	tesira.send("LogicMeter2 subscribe state 2 d22 500");
		    	tesira.send("LogicMeter2 subscribe state 3 d23 500");
		    	tesira.send("LogicMeter2 subscribe state 4 d24 500");
		    	tesira.send("LogicMeter2 subscribe state 5 d25 500");
		    	tesira.send("LogicMeter2 subscribe state 6 d26 500");
				tesira.send("LogicMeter2 subscribe state 7 d27 500");

		    	
		    	tesira.send("Mute1 subscribe mute 1 d30 1000");




//			},100); //tesira.Updatebuttons();

			setTimeout(function(){
				CF.flipToPage("Camera");

			},1000)

		}
		
    },//ParseData


    logicMeter : function (join, value, token, tags) {

    	CF.getJoin(join, function(getJoin,getValue,getTokens){ 
 
 			CF.log("Komtie hier?? " + join + " met deze join......" )
    		switch (join) {

    			case "d20" :
    				if (getValue) {    					
    					tesira.send("SourceSelector1 set sourceSelection 0");
    				}

    			case "d21" :
    				if (getValue) {
    					tesira.send("SourceSelector1 set sourceSelection 0");
    				}
    				else{    					
    					tesira.send("SourceSelector1 set sourceSelection 1");
    				}

    			break;

    			case "d22" :
    				if (getValue) {
    					tesira.send("SourceSelector1 set sourceSelection 0");
    				}
    				else{    					
    					tesira.send("SourceSelector1 set sourceSelection 2");
    				}

    			break;

    			case "d23" :
    				if (getValue) {
    					tesira.send("SourceSelector1 set sourceSelection 0");
    				}
    				else{    					
    					tesira.send("SourceSelector1 set sourceSelection 3");
    				}

    			break;

//    			case "d24" :
    				if (getValue) {
    					tesira.send("SourceSelector1 set sourceSelection 0");
    				}
    				else{    					
    					tesira.send("SourceSelector1 set sourceSelection 4");
    				}

 //   			break;

 //   			case "d25" :
    				if (getValue) {
    					tesira.send("SourceSelector1 set sourceSelection 0");
    				}
    				else{    					
    					tesira.send("SourceSelector1 set sourceSelection 5");
    				}

 //   			break;

//    		case "d26" :
    				if (getValue) {
    					tesira.send("SourceSelector1 set sourceSelection 0");
    				}
    				else{    					
    					tesira.send("SourceSelector1 set sourceSelection 6");
    				}

    			break;

    		}

    	} )//CF.getJoin(join, function(getJoin,getValue,getTokens){
    }, // logicMeter : function (join, value, token, tags) {


    mutes : function (join, value, tokens, tags) {
		CF.getJoin(join, function(getJoin,getValue,getTokens){




				if(getValue==0){
					//CF.log("Mute Log send: " + tesira.buffer.push("Nexia","SET 1 FDRMUTE " + InstanceID + " " + FaderNR + " 1"));
					tesira.send("Mute1 set mute 1 1");
//					CF.setJoin(getJoin,getValue^=1)
//					tesira.resetCounter();
				}
				else{
					//CF.log("Mute Log send: " + tesira.buffer.push("Nexia","SET 1 FDRMUTE " + InstanceID + " " + FaderNR + " 0"));
					//CF.log("Send command: " + "SET 1 FDRMUTE " + InstanceID + " " + FaderNR + " 0");
					tesira.send("Mute1 set mute 1 0");
//					tesira.resetCounter();
//					CF.setJoin(getJoin,getValue^=1)
				}





//			tesira.send("Mute1 set mute 1 " + getValue);
//			CF.setJoin(getJoin,getValue^=1)

		}); // getJoin

    },// mutes : function () {



	mutePressed : function(join, value, tokens, tags){
		//CF.log("MutePressed: Join: " + join + " Value: " + value + " tokens: " + tokens + " tags: " + tags)

			CF.getJoin(join, function(getJoin,getValue,getTokens){
				//CF.log("GetJoin: getJoin: " + getJoin + " getValue: " + getValue + " getTokens: " + getTokens);

		    	var instanceID = join.slice(1,3);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
				var input = join.slice(3);
//				CF.log("ID: " + instanceID);
//				CF.log("Input " + input);

				if(getValue==0){
					//CF.log("Mute Log send: " + tesira.buffer.push("Nexia","SET 1 FDRMUTE " + InstanceID + " " + FaderNR + " 1"));
					tesira.send("Level" + instanceID + " set mute " + input + " 1");
//					tesira.resetCounter();
				}
				else{
					//CF.log("Mute Log send: " + tesira.buffer.push("Nexia","SET 1 FDRMUTE " + InstanceID + " " + FaderNR + " 0"));
					//CF.log("Send command: " + "SET 1 FDRMUTE " + InstanceID + " " + FaderNR + " 0");
					tesira.send("Level" + instanceID + " set mute " + input + " 0");
//					tesira.resetCounter();
					CF.setJoin(getJoin,getValue^=1)
				}
			}); // getJoin

	},// Mute Pressed

    setMicLevel : function(join, Sliderlevel){
    	
    	var level = tesira.calculateRealMicLevel(Sliderlevel);	//0-65535 -> slider val is relative. omrekenen naar tesira.

    	var instanceID = join.slice(1,2)	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
		var input = join.slice(3);
		//CF.log("instance: " + instanceID + " Input: " + input)
		tesira.send("Level" + instanceID + " set level " + input + " " + level)
    },

	setMusicLevel : function(join, Sliderlevel){
//    	CF.log("SetMusic Level van de fader: " + Sliderlevel)

    },

    setPreset : function(presetName){
 //   	CF.log("Preset: " + presetName)
    	tesira.send("DEVICE recallPresetByName " + presetName)

    },



    onSliderPressed : function (join, value, tokens, tags) {
		tesira.setMicLevel(join,value);
	},

	onSliderDragged : function (join, value, tokens, tags) {
			tesira.setMicLevel(join,value);
	},

	onSliderReleased : function (join, value, tokens, tags) {
			tesira.buffer = []; // buffer leegmaken en alleen laatste waarde verzenden!
			tesira.setMicLevel(join,value);
	},

    onMusicSliderPressed : function (join, value, tokens, tags) {
		tesira.setMusicLevel(join,value);
	},

	onMusicSliderDragged : function (join, value, tokens, tags) {
			tesira.setMusicLevel(join,value);
	},

	onMusicSliderReleased : function (join, value, tokens, tags) {
			tesira.buffer = []; // buffer leegmaken en alleen laatste waarde verzenden!
			tesira.setMusicLevel(join,value);
	},



	setMicLevel : function(join, Sliderlevel){
    	var level = tesira.calculateRealMicLevel(Sliderlevel);	//0-65535 -> slider val is relative. omrekenen naar nexia.
    	var instanceID = join.slice(1,3);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
		var input = join.slice(3);
//		CF.log("Slice Fader level: " + instanceID + " " + input)
		tesira.send("Level" + instanceID + " set level " + input + " " + level + "\x0D\x0A" );
    }, //SetMicLevel	

    setMusicLevel : function(join, Sliderlevel){
    	var level = tesira.calculateRealMusicLevel(Sliderlevel);	//0-65535 -> slider val is relative. omrekenen naar nexia.
    	var instanceID = join.slice(1,3);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
		var input = join.slice(3);
//		CF.log("Slice Fader level: " + instanceID + " " + input)
		tesira.send("Level" + instanceID + " set level " + input + " " + level + "\x0D\x0A" );
    }, //SetMicLevel

    makeConnection : function(){ // eerste handshake voor openen connectie met Tesira!
    	//FF FC 18 FF FC 20 FF FC 23 FF FC 27 FF FC 24
    	//FF FE 03 FF FB 01 FF FC 22 FF FC 1F FF FE 05
 			CF.log("Sending data to connect!");
	    	CF.send(tesira.systemName, "\xFF\xFC\x18");		//Won't Termal Type
	    	CF.send(tesira.systemName, "\xFF\xFC\x20");		//Won't Termal speed
	    	CF.send(tesira.systemName, "\xFF\xFC\x23");		//Won't X Display Location
	    	CF.send(tesira.systemName, "\xFF\xFC\x27");		//Won't new environment option
	    	CF.send(tesira.systemName, "\xFF\xFC\x24");		//Won't Environment Option 

			setTimeout(function(){
		    	CF.send(tesira.systemName, "\xFF\xFE\x03");		//Don’t Suppress Go Ahead
		    	//tesira.send("\xFF\xFB\x01");		//Will echo 						
		    	CF.send(tesira.systemName, "\xFF\xFC\x01"); 		//Won't echo 	(BIAMP keuze)
		    	CF.send(tesira.systemName, "\xFF\xFC\x22");		//Won't Linemode (BIAMP keuze)
		    	CF.send(tesira.systemName, "\xFF\xFC\x1F");		//Won't Negotiate About Window Size 
		    	CF.send(tesira.systemName, "\xFF\xFE\x05");		//Don't Status 
		    },250);
	    	
	    	setTimeout(function(){
		    
		    	CF.send(tesira.systemName, "\xFF\xFC\x21");		//Won't Remote Flow Control 
		    	//tesira.send("\xFF\xFB\x21");		//Will Remote Flow Control 
		    	CF.send(tesira.systemName, "\xFF\xFD\x01");		//Do Echo (echo command's)
		    	//tesira.send("\xFF\xFE\x01");		//Don’t Echo (BIAMP keuze)
		    	CF.send(tesira.systemName, "\xFF\xFC\x06");		//Won't Timing Mark
		    	//tesira.send("\xFF\xFB\x06");		//Will Timing Mark
		    	CF.send(tesira.systemName, "\xFF\xFC\x00");		//Won't Binary Transmission
		    	CF.send(tesira.systemName, "\xFF\xFE\x03");		//Don’t Suppress Go Ahead
		    },350);

	    	setTimeout(function(){
				CF.send(tesira.systemName, "\xFF\xFD\x01");		//Do Echo
	    		CF.send(tesira.systemName, "\x0D\x0A");
	    		//tesira.send("\xFF\xFE\x01");		//Don’t Echo (BIAMP keuze)
	    		CF.log("Data Verzonden!");

	    	},450);
    },

	send : function(data){		// data toevoegen aan het einde van het buffer
    //	tesira.buffer.push(data);
    	CF.send(tesira.systemName, data + "\x0D\x0A" ); // Data direct uitspugen!
    },

    sendBuffer : function() {
	    if(tesira.buffer.length>0) {    //Verstuur data waar nodig voor de tesira
	      var command = tesira.buffer.shift();
	      //CF.log("Lengte Buffer: " + tesira.buffer.length)
	      CF.send(tesira.systemName, command + "\x0D\x0A" );
	    }
	    
	    else{
	    	//denon.getStatus();
	    	//tesira.send("LogicMeter1 get state 1");
	    } 
  	},

  	makeReadable : function (feedbackItem, matchedString){
	    var readable = "", i;
	    for (i = 0; i < matchedString.length; i++) {
	        var byteVal = matchedString.charCodeAt(i);
	        if (byteVal < 32 || byteVal > 127) {
	            readable += "\\x" + ("0" + byteVal.toString(16).toUpperCase()).slice(-2);
	        } else {
	            readable += matchedString[i];
	        }
	    }
	    return readable;
    },


	
	calculateRealMicLevel : function (val){
		var value = parseInt(val); 
		var scale = (((value/65535)*tesira.rangevaluemic)+tesira.minvaluemic); // maak van relative waarde tesira waarden
		
		return Math.round(scale);
	},


	calculateRealMusicLevel : function (val){
		var value = parseInt(val); 
		var scale = (((value/65535)*tesira.rangevaluemusic)+tesira.minvaluemusic); // maak van relative waarde tesira waarden
		
		return Math.round(scale);
	},

	calculateLevel : function (val){
		var value = parseInt(val); 
		var scale = ((value - tesira.minvaluemic)/tesira.rangevaluemic)* 65535; // maak van tesira waarden relative waarden
		return Math.round(scale);
	},

	calculateMusicLevel : function (val){
		var value = parseInt(val); 
		var scale = ((value - tesira.minvaluemusic)/tesira.rangevaluemusic)* 65535; // maak van tesira waarden relative waarden
		
		return Math.round(scale);
	},

	
}


CF.modules.push({name:"Tesira Module", setup:tesira.init}); // moet staan na de module!!
