/*
Camera aansturing door middel van visca over IP
================================================================================================================================================
Project:      	Wesotronic
Programmeur:	<PERSON>
info:       	<EMAIL>
versie:       	v3.1
datum:        	Augustus 2020
klant: 			Schoonrewoerd GK 
================================================================================================================================================
Aangemaakt om vanuit van var camera alle camera's aan te sturen
per camera is er een var cameraX om de gewenste camera aan te sturen.

V3.1 opslaan van presets toegevoegd.
als preset knop langer als 12 x 250mS wordt ingedrukt wordt niet de preset afgeroepen maar wordt de huidige stand van de camera opgeslagen onder
de betrefende preset knop.

*/



var camera = {
	bootPreset 		: 1,
	bootCamera 		: 1,

	camera1Idle 	: true,
	camera2Idle 	: true,

	//d2xxx = camera
	//d201x = cameraSelectie
	//d200x = camera move
	//d210x = camera zoom
	//d21xy = x is cam nummer | y is presetnummer
	//d290x = videoinput

	moveButtons		: ["d2001","d2002","d2003","d2004",		//UP , DOWN , LEFT , RIGHT
					   "d2005","d2006","d2007","d2008"],	//UPLEFT , UPRIGHT , DOWNLEFT , DOWNRIGHT	
	zoomButtons		: ["d2101","d2102"],					//ZOOMIN , ZOOMOUT
	presetButtons	: ["d2111","d2112","d2113","d2114","d2115","d2116","d2117","d2118","d2119",
					   "d2121","d2122","d2123","d2124","d2125","d2126","d2127","d2128","d2129"],
	cameraSelectie  : ["d2011","d2012"],
	focusButtons 	: ["d2201"], //PushFocus


	activeCamera	: 0,
	firstBoot 		: false,

	moveUp			: "\x01",
	moveDown		: "\x02",
	moveLeft		: "\x01",
	moveRight		: "\x02",
	moveStop 		: "\x03",

	zoomIn 			: "\x02",
	zoomOut 		: "\x03",
	zoomStop		: "\x00",

//Algemene camera commando's te gebruiken door iedere camera
	camAddress 		: "\x81",

	pan 			: "\x01\x06\x01",
	zooming			: "\x01\x04\x07",

	speedPan		: "\x03", //	\x01 low speed, \x18 high speed
	speedTilt		: "\x02", //	\x01 low speed, \x14 high speed
//	speedPan		: "\x05", //	\x01 low speed, \x18 high speed
//	speedTilt		: "\x04", //	\x01 low speed, \x14 high speed

	presetCall		: "\x01\x04\x3F\x02",
	presetStore		: "\x01\x04\x3F\x01",
	presetIntervalTimerID : null,
	presetStoreTimer : 0,

	stopByte		: "\xFF",
////////////////////////////////////////////////////////////

	init : function(){

		CF.log("Camera Initializing, Camera.JS!");
		camera.activeCamera = String.fromCharCode(parseInt(camera.bootCamera)); // active camera instellen als bootcamera.

		CF.watch(CF.ObjectPressedEvent, camera.moveButtons, camera.movePressed);		// Watch of een move button wordt ingedrukt (CF.event, Array met items, af te roepen functie)
    	CF.watch(CF.ObjectReleasedEvent, camera.moveButtons, camera.moveReleasd);		// watch of een move button wordt losgelaten

    	CF.watch(CF.ObjectPressedEvent, camera.presetButtons, camera.presetPressed);		// Watch of een preset button wordt ingedrukt (CF.event, Array met items, af te roepen functie)
    	CF.watch(CF.ObjectReleasedEvent, camera.presetButtons, camera.presetReleased);		// Watch of een preset button wordt ingedrukt (CF.event, Array met items, af te roepen functie)

    	CF.watch(CF.ObjectPressedEvent, camera.zoomButtons, camera.zoomPressed);		// Watch of een zoom button wordt ingedrukt (CF.event, Array met items, af te roepen functie)
    	CF.watch(CF.ObjectReleasedEvent, camera.zoomButtons, camera.zoomReleased);		// Watch of een zoom button wordt ingedrukt (CF.event, Array met items, af te roepen functie)

    	CF.watch(CF.ObjectPressedEvent, camera.cameraSelectie, camera.cameraSelect)
    	CF.watch(CF.ObjectPressedEvent, camera.focusButtons, camera.focusSelect)

    	CF.setJoin("s2110" , "" ) // STORE tekst verwijderen bij opstart
    	CF.setJoin("s2120" , "" ) // STORE tekst verwijderen bij opstart



 		switch (camera.activeCamera){
 			case 0 : 
 				CF.log("Geen camera actief")
 			break;

 			case "\x01" : 
 				CF.setJoin(camera.cameraSelectie[0],1)
 			break;
 			
 			case "\x02" :
 				CF.setJoin(camera.cameraSelectie[1],1)
 			break;
 		}// Switch
	},// init : function

	cameraSelect : function (join, value, tokens, tags){
//		CF.log("Camera Selectie")

		var tempCamera = join.slice(4,5);
		CF.setJoin("CAMERA",0);
		CF.setJoin(join,1);

		camera.activeCamera = String.fromCharCode(parseInt(tempCamera))
		

//		CF.log("active Camera: " + camera.makeReadable(null,camera.activeCamera))


	},

	focusSelect : function (join, value, tokens, tags){
		switch (camera.activeCamera){

			case "\x01" :
				camera1.focusPush()
			break;

			case "\x02" :
				camera2.focusPush()
			break;

			default :
				CF.log("focusSelect activeCamera: " + camera.activeCamera)

			break
		}//switch (camera.activeCamera)

	},

	movePressed : function (join, value, tokens, tags){
		var pan = "\x03"
		var tilt = "\x03"

		switch (join) {
			case camera.moveButtons[0] : // UP
				pan = 	camera.moveStop
				tilt = 	camera.moveUp
			break;

			case camera.moveButtons[1] : //LEFT
				pan = 	camera.moveLeft
				tilt = 	camera.moveStop
			break;			

			case camera.moveButtons[2] : //:RIGHT
				pan = 	camera.moveRight
				tilt = 	camera.moveStop
			break;			

			case camera.moveButtons[3] : //DOWN
				pan = 	camera.moveStop
				tilt = 	camera.moveDown
			break;

			case camera.moveButtons[4] : //UPLEFT
				pan = 	camera.moveLeft
				tilt = 	camera.moveUp
			break;

			case camera.moveButtons[5] : //UPRIGHT
				pan = 	camera.moveRight
				tilt = 	camera.moveUp
			break;

			case camera.moveButtons[6] : //DOWNLEFT
				pan = 	camera.moveLeft
				tilt = 	camera.moveDown
			break;

			case camera.moveButtons[7] : //DOWNRIGHT
				pan = 	camera.moveRight
				tilt = 	camera.moveDown
			break;

			default :
				CF.log("ERROR MOVE PRESSED")
			break;
		} //switch


		// IF PAN && TILT == \x03 Doe niets toevoegen
		switch (camera.activeCamera){

			case "\x01" :
				camera1.move(pan,tilt)
				CF.setJoin("PRESET1",0)
			break;

			case "\x02" :
				camera2.move(pan,tilt)
				CF.setJoin("PRESET2",0)
			break;

			default :
				CF.log("MOVEPRESED activeCamera: " + camera.activeCamera)
			break
		}//switch (camera.activeCamera)


	},//movePressed

	moveReleasd : function (join, value, tokens, tags){

		switch (camera.activeCamera){

			case "\x01" :
				camera1.move(camera.moveStop,camera.moveStop)
			break;

			case "\x02" :
				camera2.move(camera.moveStop,camera.moveStop)
			break;

			default :
				CF.log("MOVESTOP activeCamera: " + camera.activeCamera)

			break
		}//switch (camera.activeCamera)
	},//moveReleasd

	presetTimer : function (){
		camera.presetStoreTimer ++;
		CF.log("PRESET STORE TIMER FUNCTION " + camera.presetStoreTimer)

		if (camera.presetStoreTimer > 12){
				CF.setJoin("s2110" , "Opgeslagen")
				CF.setJoin("s2120" , "Opgeslagen")
		}
	},

	presetPressed : function (join, value, tokens, tags){
		CF.log("presetPressed")
		camera.presetStoreTimer = 0;
		camera.presetIntervalTimerID = setInterval(function(){camera.presetTimer();},250) //actief zetten als sendbuffer aangemaakt is.
	},//presetPressed

	presetReleased : function (join, value, tokens, tags){
//		CF.log("presetReleased")
		
		CF.setJoin("s2110" , "")
		CF.setJoin("s2120" , "")
		
		clearInterval(camera.presetIntervalTimerID)
//		CF.log("PRESET Release: " + camera.presetStoreTimer)

		var preset = "\x00"
		var tempCamera = join.slice(3,4);
		var tempPreset = join.slice(4,5);

//		CF.log("tempCamera: " + tempCamera);
//		CF.log("tempPreset: " + tempPreset);

		CF.setJoin("PRESET" + tempCamera , 0);
		CF.setJoin(join,1);

		if(camera.presetStoreTimer < 12 ){
			if (tempCamera == "1") {
//				CF.log("Camera1 CALL PRESET")
				camera1.preset(String.fromCharCode(parseInt(tempPreset)));
			}//if (tempCamera == "1") {
			if (tempCamera == "2") {
//				CF.log("Camera2 CALL PRESET")
				camera2.preset(String.fromCharCode(parseInt(tempPreset)));
			}//if (tempCamera == "2") {
		}//if(camera.presetStoreTimer < 12 ){
		else{
//			CF.log("Store Preset")

			if (tempCamera == "1") {
//				CF.log("Camera1 STORE PRESET")
				camera1.storePreset(String.fromCharCode(parseInt(tempPreset)));
			}//if (tempCamera == "1") {
			if (tempCamera == "2") {
//				CF.log("Camera2 STORE PRESET")
				camera2.storePreset(String.fromCharCode(parseInt(tempPreset)));
			}//if (tempCamera == "2") {

		}//else{


	
	},//presetReleased

	zoomPressed : function (join, value, tokens, tags){
//		CF.log("zoomPressed")

		var zoom = "\x03"

		switch (join) {
			case camera.zoomButtons[0] : // Zoom IN
				zoom = 	camera.zoomIn
			break;

			case camera.zoomButtons[1] : // Zoom OUT
				zoom = 	camera.zoomOut
			break;			

			default :
				CF.log("ERROR ZOOM PRESSED")
			break;
		} //switch


		// IF PAN && TILT == \x03 Doe niets toevoegen
		switch (camera.activeCamera){

			case "\x01" :
				camera1.zoom(zoom)
				CF.setJoin("PRESET1",0)
			break;

			case "\x02" :
				camera2.zoom(zoom)
				CF.setJoin("PRESET2",0)
			break;

			default :

			break
		}//switch (camera.activeCamera)


	},//zoomPressed

	zoomReleased : function (join, value, tokens, tags){
//		CF.log("zoomReleased")

		switch (camera.activeCamera){

			case "\x01" :
				camera1.zoom(camera.zoomStop)
			break;

			case "\x02" :
				camera2.zoom(camera.zoomStop)
			break;

			default :

			break
		}//switch (camera.activeCamera)

	},//zoomReleased



	makeReadable : function (feedbackItem, matchedString){
		var readable = "", i;
		
		for (i = 0; i < matchedString.length; i++) {
			var byteVal = matchedString.charCodeAt(i);
			
			if (byteVal < 32 || byteVal > 127) {
				readable += "\\x" + ("0" + byteVal.toString(16).toUpperCase()).slice(-2);
			} else {
				readable += matchedString[i];
			}
		}
		return readable;
	},

};
CF.modules.push({name:"VISCA IP Camera Module", setup:camera.init}); // regel moet NA var.nexia staan!



var camera1 = {

	buffer : [],

	systemName 			: "Camera1",
	feedbackName 		: "raw_Camera1",

	connectionDJoin 	: "d113",
	connectionSJoin 	: "s113",

	init : function(){

		CF.log("Camera1 Initializing, Camera.JS!");
		setInterval(function(){camera1.sendbuffer();},100) //actief zetten als sendbuffer aangemaakt is.

		CF.watch(CF.FeedbackMatchedEvent, camera1.systemName, camera1.feedbackName, camera1.parseData);              // controller op feedback
		CF.watch(CF.ConnectionStatusChangeEvent, camera1.systemName, camera1.onConnectionChange, true);

	},// init : function

	move : function (pan,tilt) {
		var command = camera.camAddress + camera.pan + camera.speedPan + camera.speedTilt + pan + tilt + camera.stopByte
//		CF.log("Command: " + camera.makeReadable(null,command))
		camera1.send("\x81\x21\xFF")
		camera1.send(command)
	},//move

	focusPush : function (){
		camera1.send("\x81\x21\xFF")
		camera1.send("\x81\x01\x04\x18\x01\xFF")
	},


	preset : function ( preset ) {
		var command = (camera.camAddress + camera.presetCall + preset + camera.stopByte);
//		CF.log("Preset data camera 1: " + camera.makeReadable(null,command));
		camera1.send("\x81\x21\xFF")
		camera1.send(command);
	},//Preset

	storePreset : function ( preset ) {
		var command = (camera.camAddress + camera.presetStore + preset + camera.stopByte);
//		CF.log("Preset data camera 1: " + camera.makeReadable(null,command));
		camera1.send("\x81\x21\xFF")
		camera1.send(command);
	}, // storePreset

	zoom : function ( inout ) {
		var command = camera.camAddress + camera.zooming + inout + camera.stopByte;
//		CF.log("Zoom Command: " + camera.makeReadable(null, command))
		camera1.send("\x81\x21\xFF")
		camera1.send(command);
	},//Zoom


	send : function(data){
		camera1.buffer.push(data);
	}, // send


	sendbuffer : function() {
		if(camera1.buffer.length>0) {    // Verstuur data waar nodig voor de Recorder
//			CF.log("Send Buffer! " + camera1.buffer.length)
			var command = camera1.buffer.shift();
			CF.send(camera1.systemName, command);
		}
		else {
			// doe hier iets als er geen data verzonden wordt
		};
	},

	parseData : function (fbName, rawdata) { 
		var matches = rawdata.match(/(.*)/);

		var COMMANDACCEPT  = rawdata.match("\x90A\xFF")
		var COMMANDDONE  = rawdata.match("\x90Q\xFF")

		if(!matches){ CF.log("no CAMERA1 RAW Return match"); return;}
//		CF.log("Return ALL CAMREA1 data" + camera.makeReadable(null,rawdata)) // Zet binnenkomende data in CF.log

		if (COMMANDACCEPT){
//			CF.log("CAMERA 1 COMMAND GEACCEPTEERD")
			camera.camera1Idle = false;
		}// IF (commandaccept)

		if (COMMANDDONE){
//			CF.log("CAMERA 1 COMMAND UITGEVOERD")
			camera.camera1Idle = true;

		}// IF (commanddone)


	}, // parseData

	onConnectionChange : function(system, connected, remote) {
		if (connected) {
			CF.log("Angekis Connected: " + "System " + system + " connected with " + remote);
    		CF.setJoin(camera1.connectionDJoin, 1);
    		CF.setJoin(camera1.connectionSJoin, remote);
/*
    		if( camera.bootCamera == 1 && camera.firstBoot == false){
    			camera1.preset(String.fromCharCode(parseInt(camera.bootPreset)))
    			CF.setJoin("d211"+camera.bootPreset,1)
    		}
*/

    	}
    	else{
			CF.setJoin(camera1.connectionDJoin, 1);
    		CF.setJoin(camera1.connectionSJoin, "no connection");
    	}//else
    }, // onConnectionChange

}//var camera1
CF.modules.push({name:"VISCA IP Camera 1 Module", setup:camera1.init}); // regel moet NA var.nexia staan!


var camera2 = {

	buffer : [],

	systemName 			: "Camera2",
	feedbackName 		: "raw_Camera2",

	connectionDJoin 	: "d114",
	connectionSJoin 	: "s114",

	init : function(){

		CF.log("Camera1 Initializing, Camera.JS!");
		setInterval(function(){camera2.sendbuffer();},100) //actief zetten als sendbuffer aangemaakt is.

		CF.watch(CF.FeedbackMatchedEvent, camera2.systemName, camera2.feedbackName, camera2.parseData);              // controller op feedback
		CF.watch(CF.ConnectionStatusChangeEvent, camera2.systemName, camera2.onConnectionChange, true);

	},// init : function

	move : function (pan,tilt) {
		var command = camera.camAddress + camera.pan + camera.speedPan + camera.speedTilt + pan + tilt + camera.stopByte
//		CF.log("Command: " + camera.makeReadable(null,command))
		camera2.send(command)
	},//move

	focusPush : function (){
		camera2.send("\x81\x21\xFF")
		camera2.send("\x81\x01\x04\x18\x01\xFF")
	},


	preset : function ( preset ) {
		var command = (camera.camAddress + camera.presetCall + preset + camera.stopByte);
//		CF.log("Preset data camera 2: " + camera.makeReadable(null,command));
		camera2.send(command);
	},//Preset

	storePreset : function ( preset ) {
		var command = (camera.camAddress + camera.presetStore + preset + camera.stopByte);
//		CF.log("Preset data camera 2: " + camera.makeReadable(null,command));
		camera2.send(command);
	}, // storePreset

	zoom : function ( inout ) {
		var command = camera.camAddress + camera.zooming + inout + camera.stopByte;
//		CF.log("Zoom Command: " + camera.makeReadable(null, command))
		camera2.send(command);
	},//Zoom


	send : function(data){
		camera2.buffer.push(data);
	}, // send


	sendbuffer : function() {
		if(camera2.buffer.length>0) {    // Verstuur data waar nodig voor de Recorder
//			CF.log("Send Buffer! " + camera2.buffer.length)
			var command = camera2.buffer.shift();
			CF.send(camera2.systemName, command);
		}
		else {
			// doe hier iets als er geen data verzonden wordt
		};
	},

	parseData : function (fbName, rawdata) { 
		var matches = rawdata.match(/(.*)/);

		var COMMANDACCEPT  = rawdata.match("\x90A\xFF")
		var COMMANDDONE  = rawdata.match("\x90Q\xFF")

	    if(!matches){ CF.log("no CAMERA2 RAW Return match"); return;}
//		CF.log("Return ALL CAMREA2 data" + camera.makeReadable(null,rawdata)) // Zet binnenkomende data in CF.log

		if (COMMANDACCEPT){
//			CF.log("CAMERA 2 COMMAND GEACCEPTEERD")
			camera.camera2Idle = false;
		}// IF (commandaccept)

		if (COMMANDDONE){
//			CF.log("CAMERA 2 COMMAND UITGEVOERD")
			camera.camera2Idle = true;


		}// IF (commanddone)

	}, // parseData

	onConnectionChange : function(system, connected, remote) {
		if (connected) {
			CF.log("Angekis Connected: " + "System " + system + " connected with " + remote);
    		CF.setJoin(camera2.connectionDJoin, 1);
    		CF.setJoin(camera2.connectionSJoin, remote);
/*
    		if( camera.bootCamera == 2 && camera.firstBoot == false){
    			camera2.preset(String.fromCharCode(parseInt(camera.bootPreset)))
    			CF.setJoin("d211"+camera.bootPreset,1)
    		}
*/

    	}
    	else{
			CF.setJoin(camera2.connectionDJoin, 1);
    		CF.setJoin(camera2.connectionSJoin, "no connection");
    	}//else
    }, // onConnectionChange

}//var camera1
CF.modules.push({name:"VISCA IP Camera 2 Module", setup:camera2.init}); // regel moet NA var.nexia staan!

