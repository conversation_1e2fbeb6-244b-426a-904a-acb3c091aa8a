/*
Project DemoKoffer:
ATEM SoftwareControl Version 8.5.2
SKAARHOJ TCP LINK V1.5.5
Inputnamen kunnen worden ingegevens uit de BlackMagic Skaarhoj.js.
alle actieve knoppen in de programmering worden geli<PERSON> met het starten van de app aangepast.
hierdoor niet nodig om alle knoppen in de programmering van een naam te voorzien.

Connection Joins:
s111, d111 Skaarhoj
s112, d112 MAgewell Streamer
s113, d113 Camera1
s114, d113 Camera2


*/


function Enum(constantsList) {
    for (var i in constantsList) {
        this[constantsList[i]] = i;
    }
}

var commStates = new Enum(['IDLE', 'RESULT_SET', 'RESULT_GET', 'BUSY'])
var connStates = new Enum(['IDLE', 'OK', 'KICK', 'TIMEOUT'])

CF.userMain = function() {

	CF.log("JS working!, main.JS");
	setTimeout({},200);
    muxlab.send("r status!");
    CF.watch(CF.NetworkStatusChangeEvent, onNetworkStatusChange, true);
//    initStreamer(); // watch op button o.a
}



function onNetworkStatusChange(networkStatus) {
    if (networkStatus.hasNetwork) {
        // note that the device may not have an IPv6 address,
        // in which case the IPv6 address string will be empty
        CF.setJoin("s10002", "Ip adres bediening: " + networkStatus.ipv4address + " verbonden via: " + networkStatus.networkType);
        log("Ip adres bediening: " + networkStatus.ipv4address);
        log("Verbonden via: " + networkStatus.networkType);

    } else {
        CF.setJoin("s10002", "No network");
        log("Geen netwerkverbinding!");
    }
}


function makeReadable(feedbackItem, matchedString){
     var readable = "", i;
    for (i = 0; i < matchedString.length; i++) {
        var byteVal = matchedString.charCodeAt(i);
        if (byteVal < 32 || byteVal > 127) {
            readable += "\\x" + ("0" + byteVal.toString(16).toUpperCase()).slice(-2);
        } else {
            readable += matchedString[i];
        }
    }
    
    return readable;
    
  }


function log(msg){

//    if (CF.debug) {
    if(false){
    CF.log("log(  " + makeReadable(null,msg) + "  )");
    }

    else {
        timestamp = this.createTimeStamp();
        CF.log("log(  " + makeReadable(null,msg) + "  )");

        CF.listAdd("l1", [{
            "s20": makeReadable(null,msg) ,
            "s21": timestamp                
        }]);
        
        setTimeout(function() {
            CF.listInfo("l1", function(list, count, first, numVisible, scrollPosition) {
            pos = count;
        });}, 200);

        setTimeout(function() {
            CF.listScroll("l1", pos ,CF.BottomPosition, true, true);
        }, 1000);

    }

}

function createTimeStamp() {
    var logDate = new Date();
//    CF.setJoin("s2" , logDate.getHours()+":"+("0"+logDate.getMinutes()).slice(-2)+":"+("0"+logDate.getSeconds()).slice(-2) )
    return logDate.getHours()+":"+("0"+logDate.getMinutes()).slice(-2)+":"+("0"+logDate.getSeconds()).slice(-2);
}