/*
20220118a
PKO

Navigeren door meerdere pagina's
*/

var navigatie = {

	buttons : ["d310" , "d320" , "d330" , "d350"],

	init : function () {
		log("navigatie.init")

		CF.watch(CF.ObjectPressedEvent, navigatie.buttons, navigatie.pages);


	},//init : function

	pages : function (join, value, tokens, tags) {

		CF.getJoin(join, function(getJoin,getValue,getTokens){
			if(getValue==0){
				CF.setJoin("NAVIGATIE" , 0 )
				CF.setJoin(getJoin,getValue^=1)

				switch(getJoin){
 			
					case navigatie.buttons[0] :
						CF.flipToPage("Camera");
						CF.setJoin("s30" , "  Bediening Kerkzaal")
					break;

 					case navigatie.buttons[1] :
						CF.flipToPage("Kerkzaal");
						CF.setJoin("s30" , "  Bediening zaal 1")
					break;
 			
					case navigatie.buttons[2] :
						CF.flipToPage("Matrix");
						CF.setJoin("s30" , "  Bediening zaal 2")
					break;
 			
					case navigatie.buttons[3] :
						CF.flipToPage("Zaal_3");
						CF.setJoin("s30" , "  Bediening Zaal 3")
					break;
					}

			}

			else{
			}

		}); // getJoin

	}, // pages : function



}//var navigatie
CF.modules.push({name:"Tesira Module", setup:navigatie.init}); // moet staan na de module!!