/*  Nexia IP Control module for Commandfusion
Project:      	Wesotronic
Programmeur: 	<PERSON>
info:       	<EMAIL>
versie:       	V2.0
datum:        	Feb 2021
================================================================================================================================================
Omschrijving:
CF aansturing Nexia voor gebruik Android touchpannels

Instellingen System Manager CommandFusion:
Ip adres/Hostname	: *************
Remote Port 		: 23

Mode 				: Always ON

End of message 		: \x0D\x0A

Feedback 			: raw_Nexia
Regex 				: .


Aanpassing voor gebruik van DETAILD Commando's
GET 1 FDRLVL  43 1 {0D}{0A}		GET 1 FDRLVL  43 1{0D}{0A}	-10.000000 {0D}{0A}
GETD 1 FDRLVL  43 1 {0D}{0A}	GETD 1 FDRLVL  43 1{0D}{0A}	#GETD 1 FDRLVL  43 1 -10.000000 {0D}{0A}

to DO,
Regel 230, 
Source select nog maken/aanpassen

Zowel met opstarten wordt er een update gedraaid,
en omdat er een orientation event ontstaat bij opstart word de verbinding verbroken en gemaakt.
Hierdoor komen er bij opstart 2 updates achter elkaar.
even bedenken hoe dit voorkomen kan worden.
*/



var nexia = {		
	buffer				: [],
	systemName			: "Nexia",
	feedbackName		: "raw_Nexia",
	connectionJoin		: "d116",
	connectionString	: "s116",
	interval			: 10000,
	counter				: 0,
	counterMax 			: 200, 
	counterAutomix		: 0,	

    minvaluemic			: -12,		// MIN waarde van de slider Microfoon in GUI
    rangevaluemic		: 20,		// Range van MIN naar MAX van slider Microfoon in GUI
    minvaluemusic		: -30,		// Min waarde van de slider Muziek in GUI
    rangevaluemusic		: 38,		// Range van MIN naar MAX van slider Muziek in GUI

    connState			: connStates.IDLE,
	connTimeout			: 0,


	//Opbouw JoinNummer: Unit nummer [1] + InstanceID [3] + kanalen [2] oftwel 6 cijfers joinnummer
 
	micSliders			: [ "a103701" , "a108002" , "a108003" , "a108004" , "a108005" , "a108006" , "a108007" , "a108008" , "a108009" , "a108010" , "a108011" , "a108012"],	// op deze sliders wordt gelet met een functie!
	musicSliders 		: [ ],
	muteButtons			: [ "d108010" , "d108104" , "d108001" , "d108002" , "d108003" , "d108004" , "d108005" , "d108006" , "d108007" , "d108008" , "d108009" , "d108010" , "d108011" , "d108012" , "d108501"],
	
	logicMeter			: [ ],	//48 is logic meter, 1-6 zijn de kanalen
	
	sourceButtons		: [  ],

	inputBlock 			: [  ],

	currentSource   	: 0,

	presetButtons		: ["d501","d502","d503","d504","d505","d506"],

	couterAutomixMaximum : 5, // Set interval X couterAutomixMaximum is tijd tussen check automixer

	init : function(){

		CF.log("Nexia Initializing, Nexia.JS!");
//		setInterval(function(){nexia.sendbuffer();},100)		// Verstuur iedere 100 mS data, als data in buffer aanwezig


		nexia.commState = commStates.IDLE
		nexia.connect(connStates.IDLE)

		CF.watch(CF.FeedbackMatchedEvent, nexia.systemName, nexia.feedbackName, nexia.parseData)              // controller op feedback

		// Sliders WATCH of bediend!
		CF.watch(CF.ObjectPressedEvent,  nexia.micSliders, nexia.onSliderPressed); 	// Watch of er een slider wordt ingedrukt
    	CF.watch(CF.ObjectDraggedEvent, nexia.micSliders, nexia.onSliderDragged); 		// Watch of er een slider wordt Gesleept	
    	CF.watch(CF.ObjectReleasedEvent, nexia.micSliders, nexia.onSliderReleased); 	// Watch of er een slider wordt Losgelaten

    	CF.watch(CF.ObjectPressedEvent,  nexia.musicSliders, nexia.onMusicSliderPressed); 	// Watch of er een slider wordt ingedrukt
    	CF.watch(CF.ObjectDraggedEvent, nexia.musicSliders, nexia.onMusicSliderDragged); 		// Watch of er een slider wordt Gesleept	
    	CF.watch(CF.ObjectReleasedEvent, nexia.musicSliders, nexia.onMusicSliderReleased); 	// Watch of er een slider wordt Losgelaten

    	CF.watch(CF.ObjectPressedEvent, nexia.muteButtons, nexia.mutePressed);		// Watch of een mute button wordt ingedrukt
    	CF.watch(CF.ObjectPressedEvent, nexia.sourceButtons, nexia.SourceSelect);	// Watch of souce select button wordt ingedrukt

    	CF.watch(CF.ObjectPressedEvent, nexia.inputBlock, nexia.inputSelect);

		CF.watch(CF.ObjectPressedEvent, nexia.presetButtons, nexia.presetPressed);		// Watch of een mute button wordt ingedrukt

    	CF.watch(CF.ConnectionStatusChangeEvent, nexia.systemName, nexia.onConnectionChange, true);

	   	CF.watch(CF.OrientationChangeEvent, nexia.onOrientationChange, true);



	}, // INIT

	connect : function(immediateState){
 		if (immediateState){
			nexia.connState = immediateState
		}
		
		switch(nexia.connState){
 			
			case connStates.IDLE:
				CF.setJoin("s1" , "IDLE");
				nexia.connState = connStates.KICK
				nexia.connTimeout = setTimeout(function(){nexia.connect()}, 3000);
				break;
			
			case connStates.OK: //Niets te doen behalve nieuwe poging starten
				CF.setJoin("s1" , "OK");
				clearTimeout(nexia.connTimeout)
				nexia.connState = connStates.KICK
				nexia.connTimeout = setTimeout(function(){nexia.connect()}, 3000);
				break;
			
			case connStates.KICK: //Tijd voor een update
				CF.setJoin("s1" , "KICK");
				nexia.connState = connStates.TIMEOUT
				nexia.sendUpdate("GETD 1 IPADDR");
				//nexia.Updatebuttons()
				
				clearTimeout(nexia.connTimeout)
				
				nexia.connTimeout = setTimeout(function(){nexia.connect()}, 1000);
				break;
			
			case connStates.TIMEOUT: //Geen antwoord
				CF.setJoin("s1" , "TIMEOUT");
				CF.setSystemProperties(nexia.systemName,{enabled: false});
				CF.setSystemProperties(nexia.systemName,{enabled: true});
				clearTimeout(nexia.connTimeout)
				nexia.connTimeout = setTimeout(function(){nexia.connect()}, 3000);
		}
 	},

	onOrientationChange: function (pageName, newOrientation) {
		//Bij opnieuw opnemen pagina komt er een orientation event.
		//door dis-connect en connect worden alle knoppen geupdate,
		// net als na het wisselen van een pagina.

    	CF.log("Orientation of page " + pageName + " changed to: " + newOrientation);
//    	CF.setSystemProperties(nexia.systemName,{enabled: false});
//   	CF.setSystemProperties(nexia.systemName,{enabled: true});
//		nexia.resetCounter();
 	},

    Updatebuttons : function(){ // automatische alle array's met mute's en sliders updaten.
		var sizeMute  = nexia.muteButtons.length;
		var sizeLevelMic = nexia.micSliders.length;
		var sizeLevelMusic = nexia.musicSliders.length;
		var sizelogicMeter = nexia.logicMeter.length;
		var sizeSourceSelect = nexia.sourceButtons.length;
		var sizeInputBlock = nexia.inputBlock.length;



    	if(sizeLevelMic >= 1){
			for (var i = sizeLevelMic - 1; i >= 0; i--) {
				var unit = nexia.micSliders[i].slice(1,2);
				var instanceID = nexia.micSliders[i].slice(2,5);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
				var input = nexia.micSliders[i].slice(5);
				nexia.sendUpdate("GETD " + unit + " FDRLVL " +  instanceID + " " + input);  // fader totaal volume
			}//for (var i = size - 1; i >= 0; i--) {
    	}//if(SizeLevel >= 1){
    	else{ CF.log("sizeLevelMic == 0") }

    	if(sizeLevelMusic >= 1){
			for (var i = sizeLevelMusic - 1; i >= 0; i--) {
				var unit = nexia.musicSliders[i].slice(1,2);
				var instanceID = nexia.musicSliders[i].slice(2,5);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
				var input = nexia.musicSliders[i].slice(5);
				nexia.sendUpdate("GETD " + unit + " FDRLVL " +  instanceID + " " + input);  // fader totaal volume
			}//for (var i = size - 1; i >= 0; i--) {
    	}//if(SizeLevel >= 1){
    	else{ CF.log("sizeLevelMusic == 0") }
	
		if(sizeMute >= 1){
			for (var i = sizeMute - 1; i >= 0; i--) {
				var unit = nexia.muteButtons[i].slice(1,2);
				var instanceID = nexia.muteButtons[i].slice(2,5);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
				var input = nexia.muteButtons[i].slice(5);
				nexia.sendUpdate("GETD " + unit + " FDRMUTE " +  instanceID + " " + input); // fader mute mic 1
			}//for (var i = size - 1; i >= 0; i--) {
    	}//if(size <= 1){
    	else{ CF.log("SizeMute == 0") }

    	if(sizelogicMeter >=1) {
			for (var i = sizelogicMeter - 1; i >= 0; i--) {
				var unit = nexia.logicMeter[i].slice(1,2);
				var instanceID = nexia.logicMeter[i].slice(2,5);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
				var channel = nexia.logicMeter[i].slice(5);

				nexia.sendUpdate("GETD " + unit + " LGCMTRSTATE " + instanceID + " " + channel); // fader mute mic 1

			}//for (var i = size - 1; i >= 0; i--) {

    	}//if(sizelogicMeter >=1) {
    	else{ CF.log("LogicMeter == 0") }

    	if(sizeSourceSelect >=1 ) {
    		var currentSource = " "
			for (var i = sizeSourceSelect - 1; i >= 0; i--) {
				var unit = nexia.sourceButtons[i].slice(1,2);
				var instanceID = nexia.sourceButtons[i].slice(2,5);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
				var channel = nexia.sourceButtons[i].slice(5);

				if (instanceID != currentSource){
					nexia.sendUpdate("GETD " + unit + " SRCSELSRC " + instanceID + " 01"); // fader mute mic 1
					currentSource = instanceID;
				}

			}//for (var i = size - 1; i >= 0; i--) {

    	}//if(sizeSourceSelect >=1 ) {
    	else{ CF.log("SourceSelect == 0") }

		if(sizeInputBlock >= 1){
			for (var i = sizeInputBlock - 1; i >= 0; i--) {
				var unit = nexia.inputBlock[i].slice(1,2);
				var instanceID = nexia.inputBlock[i].slice(2,5);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
				var input = nexia.inputBlock[i].slice(5);
				nexia.sendUpdate("GETD " + unit + " PHPWR " +  instanceID + " " + input); // fader mute mic 1
				nexia.sendUpdate("GETD " + unit + " INPGAIN " +  instanceID + " " + input); // fader mute mic 1

			}//for (var i = size - 1; i >= 0; i--) {
    	}//if(size <= 1){
    	else{ CF.log("SizeMute == 0") }




    }, //Updatebuttons : function(){

	onConnectionChange : function(system, connected, remote) {
		if (connected) {
    		
			CF.log("Nexia Connected: " + "System " + system + " connected with " + remote);
    		CF.setJoin(nexia.connectionJoin , 1);
    		CF.setJoin(nexia.connectionString , remote);
    		//CF.setjoin("s112",networkStatus.ipv4address);
	   		nexia.Updatebuttons();
    	}
    	else{
			CF.setJoin(nexia.connectionJoin, 0);
    		CF.setJoin(nexia.connectionString , "No Connection");
    	}
    }, // onConnectionChange

	presetPressed: function(join, value, tokens, tags) {
		// Only do something on the falling edge (value=1 on press or your particular setup)
		// If your buttons send 1 when pressed, check that first or adapt logic as needed
		if (value) {
		let presetNumber = 1001; // Default
		switch (join) {
		case "d501": presetNumber = 1001; break;
		case "d502": presetNumber = 1002; break;
		case "d503": presetNumber = 1003; break;
		case "d504": presetNumber = 1004; break;
		case "d505": presetNumber = 1005; break;
		case "d506": presetNumber = 1006; break;
		}
		// Recall the chosen preset
		nexia.sendUser("RECALL 0 PRESET " + presetNumber);
		
			// Then ask Nexia which of the 6 is active:
			nexia.sendUser("GETD 0 PRESET 1001");
			nexia.sendUser("GETD 0 PRESET 1002");
			nexia.sendUser("GETD 0 PRESET 1003");
			nexia.sendUser("GETD 0 PRESET 1004");
			nexia.sendUser("GETD 0 PRESET 1005");
			nexia.sendUser("GETD 0 PRESET 1006");
		}
		},

    onSliderPressed : function (join, value, tokens, tags) {
		nexia.setMicLevel(join,value);
	},

	onSliderDragged : function (join, value, tokens, tags) {
    	nexia.setMicLevel(join,value);
	},

	onSliderReleased : function (join, value, tokens, tags) {
    	nexia.buffer = []; // gooi het buffer leeg en stuur de laatste waarde bij release naar de nexia!
		nexia.setMicLevel(join,value);
	},

	onMusicSliderPressed : function (join, value, tokens, tags) {
		nexia.setMusicLevel(join,value);
	},

	onMusicSliderDragged : function (join, value, tokens, tags) {
    	nexia.setMusicLevel(join,value);
	},

	onMusicSliderReleased : function (join, value, tokens, tags) {
    	nexia.buffer = []; // gooi het buffer leeg en stuur de laatste waarde bij release naar de nexia!
		nexia.setMusicLevel(join,value);
	},


	setMicLevel : function(join, Sliderlevel){
    	var level = nexia.calculateRealMicLevel(Sliderlevel);	//0-65535 -> slider val is relative. omrekenen naar nexia.
//	   	CF.log("SetMicLevel: " + level);
		var unit = join.slice(1,2)
    	var instanceID = join.slice(2,5);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
		var input = join.slice(5);
		CF.setJoin("s" + unit + instanceID + input , level)
//		nexia.resetCounter();
		nexia.sendUpdate("SETD " + unit + " FDRLVL "+ instanceID + " " + input + " " + level)
    },//setMicLevel : function(join, Sliderlevel){

    setMusicLevel : function(join, Sliderlevel){
    	var level = nexia.calculateRealMusicLevel(Sliderlevel);	//0-65535 -> slider val is relative. omrekenen naar nexia.
//	   	CF.log("SetMicLevel: " + level);
		var unit = join.slice(1,2)
    	var instanceID = join.slice(2,5);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
		var input = join.slice(5);
		CF.setJoin("s" + unit + instanceID + input , level)
//		nexia.resetCounter();
		nexia.sendUpdate("SETD " + unit + " FDRLVL "+ instanceID + " " + input + " " + level)
    },//setMusicLevel : function(join, Sliderlevel){


    mutePressed : function(join, value, tokens, tags){
		//CF.log("MutePressed: Join: " + join + " Value: " + value + " tokens: " + tokens + " tags: " + tags)
		nexia.buffer = []; //evt update leegooien.

		CF.getJoin(join, function(getJoin,getValue,getTokens){
			var unit = join.slice(1,2)
	    	var instanceID = join.slice(2,5);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
			var input = join.slice(5);

			if(getValue==0){
				nexia.sendUser("SETD " + unit + " FDRMUTE " + instanceID + " " + input + " 1");
//				nexia.resetCounter();
				CF.setJoin(getJoin,getValue^=1)
			}//if(getValue==0){

			else{
				nexia.sendUser("SETD " + unit + " FDRMUTE " + instanceID + " " + input + " 0");
//				nexia.resetCounter();
				CF.setJoin(getJoin,getValue^=1)
			}
		}); // getJoin
	},// Mute Pressed

	SourceSelect : function(join, value, tokens, tags){

		CF.getJoin(join, function(join, getValue, tokens) {
			var unit = join.slice(1,2)
			var instanceID = join.slice(2,5);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
			var input = join.slice(5);

			nexia.buffer = []; // Clear het bestaande buffer			
//			nexia.resetCounter(); // zet counter naar 0, (counter telt timeout voor update!)
			
			if (getValue){
				nexia.sendUser("SETD " + unit + " SRCSELSRC " + instanceID + " 1 0");
				CF.setJoin( "d" + unit + instanceID + "00" , 0 );
			}
			else{
				nexia.sendUser("SETD " + unit + " SRCSELSRC " + instanceID + " 1 " + input);
				CF.setJoin( "d" + unit + instanceID + "00" , 0 );
				CF.setJoin( join , 1 );	
			}
		});
	},

	inputSelect : function(join, value, tokens, tags){

		CF.getJoin(join, function(getJoin, getValue, tokens) {
			var unit = join.slice(1,2)
			var instanceID = join.slice(2,5);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
			var input = join.slice(5);

			nexia.buffer = []; // Clear het bestaande buffer			
//			nexia.resetCounter(); // zet counter naar 0, (counter telt timeout voor update!)

			//switch (matches[2]){
			switch(tags[0]) {
				case "PHANTOM" : 
					CF.log("CASE PHANTOM......")
					if(getValue==0){
						nexia.sendUser("SETD " + unit + " PHPWR " + instanceID + " " + input + " 1");
//						nexia.resetCounter();
						CF.setJoin(getJoin,getValue^=1)
					}//if(getValue==0){

					else{
						nexia.sendUser("SETD " + unit + " PHPWR " + instanceID + " " + input + " 0");
//						nexia.resetCounter();
						CF.setJoin(getJoin,getValue^=1)
					}
				break;
				case "INCREASE" : 
					CF.log("CASE INCREASE......")
					nexia.sendUser("INC " + unit + " INPGAIN " + instanceID + " " + input);
					nexia.sendUser("GETD " + unit + " INPGAIN " +  instanceID + " " + input); // fader mute mic 1
					

				break;
				case "DECREASE" : 
					CF.log("CASE DECREASE......")
					nexia.sendUser("DEC " + unit + " INPGAIN " + instanceID + " " + input);				
					nexia.sendUser("GETD " + unit + " INPGAIN " +  instanceID + " " + input); // fader mute mic 1
				break;

				default : 

					CF.log("TAGS in defaultcase: " + tags)
				break;

			}//switch(tags)



		});
	},




    calculateRealMicLevel : function (val){
		var value = parseInt(val); 
		var scale = (((value/65535)*nexia.rangevaluemic)+nexia.minvaluemic); // maak van relative waarde nexia waarden
		
		return Math.round(scale);
	},

    calculateRealMusicLevel : function (val){
		var value = parseInt(val); 
		var scale = (((value/65535)*nexia.rangevaluemusic)+nexia.minvaluemusic); // maak van relative waarde nexia waarden
		
		return Math.round(scale);
	},

	calculateLevel : function(val){
		var value = parseInt(val); 
		var scale = ((value - nexia.minvaluemic)/nexia.rangevaluemic)* 65535;
		return Math.round(scale);
	},

	calculateMusicLevel : function(val){
		var value = parseInt(val); 
		var scale = ((value - nexia.minvaluemusic)/nexia.rangevaluemusic)* 65535;
		return Math.round(scale);
	},

	makeReadable : function (feedbackItem, matchedString){
		var readable = "", i;
		
		for (i = 0; i < matchedString.length; i++) {
			var byteVal = matchedString.charCodeAt(i);
			
			if (byteVal < 32 || byteVal > 127) {
				readable += "\\x" + ("0" + byteVal.toString(16).toUpperCase()).slice(-2);
			} else {
				readable += matchedString[i];
			}
		}
		return readable;
	},

    sendUser : function(data){
    	//UnShift, voor data aan het begin van een array plaatsen
		nexia.buffer.push(data);
		nexia.sendbuffer();
 	},

 	sendUpdate : function (data){
    	//UnShift, voor data aan het begin van een array plaatsen
		nexia.buffer.unshift(data);
		nexia.sendbuffer();

 	},

	sendbuffer : function() {
		if(nexia.buffer.length>0) {    // Verstuur data waar nodig voor de Recorder
			var command = nexia.buffer.shift();
			CF.send(nexia.systemName, command + "\x0D\x0A" );
		}

//		else {
//			if (nexia.counter >= nexia.counterMax){
//				nexia.Updatebuttons();
//				nexia.resetCounter();
//			} // IF nexia.counter

//			if (nexia.counterAutomix >= nexia.couterAutomixMaximum){
//				nexia.CheckAutomixer();
//			} // IF nexia.checkautomixcounter
		
//			nexia.counter = nexia.counter + 1;
//			nexia.counterAutomix = nexia.counterAutomix + 1;
//		};

	},//sendbuffer : function() {






/*	send : function(data){
		nexia.buffer.push(data);
//		CF.log("Data Send= " + data)

	},
*/
/*		nexia.commState = commStates.BUSY
		
		//nexia.connState = connStates.TIMEOUT

		CF.send(nexia.systemName, data + "\x0D\x0A");
*/





    parseData : function (fbName, rawdata) { 
 		nexia.connect(connStates.OK)


    	var matches = rawdata.match(/(.*)/);
	    if(!matches){ CF.log("no NEXIA RAW Return match"); return;}
	    CF.log("BINNENKOMENDE DATA:" + nexia.makeReadable(null,rawdata)) // Zet binnenkomende data in CF.log

//	    var HASHTAG		= rawdata.match("#");
	    var NOTOKE 		= rawdata.match("-ERR:");
	    var HASHTAGGETD 	= rawdata.match("#GETD ")
	    // OKE wordt nu niet gebruikt
	    var OKE 		= rawdata.match(" +OK");
	    var IPADRESS	= rawdata.match("#GETD 1 IPADDR")

	    if (NOTOKE){
	    	CF.log("NOT OKE: " + nexia.makeReadable(null,rawdata))
	    }//if (NOTOKE){
	    else{


	    	if(IPADRESS){
	    		CF.log("IP address voor openhouden verbinding")
	    	}
	    	else{
		    	if(HASHTAGGETD){
	//	    		CF.log("HASHTAG DATA: " + nexia.makeReadable(null,rawdata))
		    		var matches = rawdata.match(/#GETD (\d) (\D+) (\d+) (\d+) (.*)\x0D\x0A/); //\D+ Meerdere non Digit characters | \d+ meerder digit charaters

		    		switch (matches[2]){

						case "PRESET":
				// matches[1]  => device ID (e.g. "0")
				// matches[3]  => preset number (e.g. "1001","1002", etc.)
				// matches[4]  => "1" if active, "0" if not
				let presetNum  = matches[3];
				let presetStat = parseInt(matches[4],10);

				// For convenience, turn them all OFF first:
				CF.setJoin("d501", 0);
				CF.setJoin("d502", 0);
				CF.setJoin("d503", 0);
				CF.setJoin("d504", 0);
				CF.setJoin("d505", 0);
				CF.setJoin("d506", 0);

				// Then only switch one ON if it’s actually active:
				if (presetStat === 1) {
					switch (presetNum) {
						case "1001": CF.setJoin("d501", 1); break;
						case "1002": CF.setJoin("d502", 1); break;
						case "1003": CF.setJoin("d503", 1); break;
						case "1004": CF.setJoin("d504", 1); break;
						case "1005": CF.setJoin("d505", 1); break;
						case "1006": CF.setJoin("d506", 1); break;
					}
				}
				break;
				
		    			case "FDRLVL" :
		    				var sizeLevelMic = nexia.micSliders.length;
							var sizeLevelMusic = nexia.musicSliders.length;

							var tempJoin = "a" + matches[1] + matches[3] + matches[4];

		    				CF.setJoin("s" + matches[1]  + matches[3] + matches[4] , parseInt(matches[5])); // schrijf fader waarde in String

							if(sizeLevelMic >= 1){ // controleer of feedback waarde in micLevel array staat
								for (var i = sizeLevelMic - 1; i >= 0; i--) {
									if (tempJoin == nexia.micSliders[i]){
										CF.setJoin(tempJoin , nexia.calculateLevel(matches[5]))
									}//if tempJoin == nexia.micSliders[i]{
								}//for (var i = size - 1; i >= 0; i--) {
					    	}//if(SizeLevel >= 1){

					    	if(sizeLevelMusic >= 1){ // controleer of feedback waarde in musicLevel array staat
								for (var i = sizeLevelMusic - 1; i >= 0; i--) {
									if (tempJoin == nexia.musicSliders[i]){
										CF.setJoin(tempJoin , nexia.calculateMusicLevel(matches[5]))
									}//if tempJoin == nexia.micSliders[i]{
								}//for (var i = size - 1; i >= 0; i--) {
					    	}//if(SizeLevel >= 1){

		    			break;

		    			case "FDRMUTE" :
		    				CF.setJoin("d" + matches[1] + matches[3] + matches[4] , matches[5].slice(0,1))
		    			break;

		    			case "LGCMTRSTATE" : 
		    				CF.setJoin("d" + matches[1]  + matches[3] + matches[4] , matches[5].slice(0,1))
		    			break;

		    			//nog nazien op nieuwe stijl Joinnummering
		    			case "SRCSELSRC" :
		    				//CF.setJoin("d" + matches[2] + "0" , 0 );

		    				tempJoin = "d" + matches[1] + matches[3] + "0" + matches[5].slice(0,1)
		    				CF.log("TempJoin: " + tempJoin)

							CF.getJoin(tempJoin, function(join, value, tokens) {
								if (value){
	//							CF.log("Als ik dit zie knippert de knop niet!")
								}
								else{
									CF.setJoin("d" + matches[1] + matches[3] + "00" , 0 );
			    					if (matches[5].slice(0,1) != "0"){
			    						CF.setJoin("d" + matches[1] + matches[3] + "0" + matches[5].slice(0,1) , 1 );
			    						CF.log("SetJoin: " + "d" + matches[1] + matches[3] + "0"  + matches[5])
			    					}
								}
							});
		    			break;

		    			case "PHPWR" :
	//	    				CF.log("CASE PHPWR: " + matches[1] + matches[3] + matches[4] + " state: " + matches[5] )
		    				CF.setJoin("d" + matches[1] + matches[3] + matches[4] , matches[5].slice(0,1))
		    			break;

		    			case "INPGAIN" :
		    				//CF.log("CASE INPGAIN: " + matches[1] + matches[3] + matches[4] + " state: " + matches[5] )
		    				CF.setJoin("s" + matches[1]  + matches[3] + matches[4] , parseInt(matches[5])); // schrijf fader waarde in String
		    				//CF.setJoin("d" + matches[1] + matches[3] + matches[4] , matches[5].slice(0,1))
		    			break;

		    			default : 
		    				CF.log("Matches[2] " + matches[2]);
		    			break;

		    		}//switch (machtes[0]){
		    	}//if(HASHTAG){

		    }//If IP Adres ELSE

	    }//IF ELSE NOTOKE



    },//parseData : function (fbName, rawdata) { 


/*
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

						case "SRCSELSRC" :
//							CF.log("SRCSELSRC: " + LastGETJOIN);
//							CF.log("Feedbackstring: " + rawdata);
							CF.setJoin("OPNAME",0)

							if(rawdata.match("0")){
								CF.log("Case 0");
								CF.setJoin("d660","1");
							}
							
							if(rawdata.match("1")){
								CF.log("Case 1");
								CF.setJoin("d661","1");
							}

							if(rawdata.match("2")){
								CF.log("Case 2");
								CF.setJoin("d662","1");
							}

							if(rawdata.match("3")){
								CF.log("Case 3");
								CF.setJoin("d663","1");
							}

						break;
*/


	CheckAutomixer : function(){
	// Check automixerfunctie in deze niet van toepassing,
	// geen automatische video sturing.
/*
		var sizelogicMeter  = nexia.logicMeter.length;
	
		if(sizelogicMeter >= 1){
			for (var i = sizelogicMeter - 1; i >= 0; i--) {
				var instanceID = nexia.logicMeter[i].slice(1,3);	// Geeft het instance ID uit de aansturing weer Join A21 = analog, instance 2, input 1
				var input = nexia.logicMeter[i].slice(3);
//				CF.log("InstanceID: " + instanceID + " input: " + input)
				nexia.send("GET 1 LGCMTRSTATE " +  instanceID + " " + input); // fader mute mic 1
			}//for (var i = size - 1; i >= 0; i--) {
    	}//if(size <= 1){
			// Check de status van de automixer (lees de stauts van de indicatoren achter de automixer of op andere posities)
*/
	},

}; // bracket var.nexia

CF.modules.push({name:"Nexia Module", setup:nexia.init}); // regel moet NA var.nexia staan!
